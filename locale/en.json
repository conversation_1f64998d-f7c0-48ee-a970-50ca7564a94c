{"full_app_name": "Pdt Attendance App", "powered_by": "Powered by PDT Solutions", "welcome_back": "Welcome back to the app", "email": "Email Address", "phone_number_invalid": "Invalid mobile number", "phone_number_required": "Mobile number is required", "email_required": "Email is required", "password_required": "Password  is required", "email_invalid": "Invalid email address", "login": "LOGIN", "user_name": "User Name", "user_name_hint": "user name", "password": "Password", "password_hint": "password", "forgot_password": "Forgot your password?", "click_to_reset": "Click here to reset it", "dont_have_account": "Don't have an account?", "create_account": "Create Account", "or_through": "Or through", "uae_pass_register": "Register with the UAE Pass digital identity", "copyright_text": "© All rights reserved to Sharjah Private Education Authority SPEA", "username_required": "Username is required", "hello": "Hello", "welcome_user": "Welcome User", "default_email": "<EMAIL>", "registered_children": "Registered children", "unregistered_children": "Unregistered children", "sons_withdrawn": "Sons withdrawn", "sons_finished_studying": "Sons who finished studying", "add_new_son": "Add New Son", "delegation": "Delegation", "certificates": "Certificates", "requests": "Requests", "show_all": "Show All", "request_type": "Request Type", "request_status": "Request Status", "search": "Search", "reset": "Reset", "request_number": "Request Number:", "request_related_to": "Request Related To:", "school": "School:", "curriculum": "Curriculum:", "created_by": "Created By:", "request_date": "Request Date:", "modification_date": "Modification Date:", "details": "Details", "pending": "Pending", "academic_sequence_certificate": "Academic Sequence Certificate", "no_requests_found": "No Requests Found", "not_available": "N/A", "pay": "Pay", "view": "View", "rejected": "Rejected", "approved": "Approved", "completed": "Completed", "paid": "Paid", "free": "Free", "pending_processing": "Under Processing", "approved_status": "Approved", "ready_for_download": "Ready for Download", "rejected_status": "Rejected", "paid_status": "Paid", "payment_required": "Payment Required", "fees_amount": "<PERSON><PERSON> Amount", "currency": "SAR", "pending_review": "Under Review", "completed_status": "Completed", "onboarding_title_1": "Track all your children's school details", "onboarding_subtitle_1": "You can manage your children's school files through Daleel platform 1", "onboarding_title_2": "Submit requests and follow up procedures", "onboarding_subtitle_2": "You can manage your children's school files through Daleel platform 2", "onboarding_title_3": "Track all your children's school details", "onboarding_subtitle_3": "You can manage your children's school files through Daleel platform 3", "submitARegistrationRequest": "Submit a registration request", "reRegistration": "Re-registration", "next": "Next", "home_tab": "Home", "certificates_tab": "Certificates", "requests_tab": "Requests", "chats_tab": "Chats", "my_profile": "My Account", "edit_profile": "Edit Profile", "family_name": "Family Name:", "nationality": "Nationality:", "gender": "Gender:", "mobile_number": "Mobile Number:", "phone_number": "Phone Number:", "registration_date": "Registration Date:", "logout_button": "Logout", "male": "Male", "female": "Female", "emirati": "Emirati", "position": "Position:", "logoutConfirmation": "Do you want to logout?", "confirm": "Confirm", "cancel": "Cancel", "guardian": "Guardian", "defaultProfileName": "<PERSON>", "defaultProfilePhone": "+***********", "defaultProfileDate": "22 January 2025 2:00PM", "No_certificates_found": "No Certificates Found", "re_registration": "Re-registration", "plz_wait": "Please Wait", "notifications": "Notifications", "no_notifications": "No Notifications", "retry": "Retry", "new_notification": "New", "notification_error": "Error loading notifications", "pull_to_refresh": "Pull to refresh", "load_notification": "Loading notifications...", "load_more": "Loading More...", "No_notifications": "No new notifications at the moment\nYou will be notified when new notifications arrive", "Something_went_wrong": "Something went wrong"}