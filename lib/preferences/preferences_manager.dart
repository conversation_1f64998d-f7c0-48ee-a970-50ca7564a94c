import 'dart:convert';
import 'package:daleel/preferences/preferences_keys.dart';
import 'package:daleel/utils/preferences/preferences_utils.dart';
import 'package:daleel/features/login/data/models/login_response_api_model.dart';

class PreferencesManager {

  Future<bool> clearData() async {
    final String? locale = await getLocale();
    await PreferencesUtils.clearData();
    if (locale != null) await setLocale(locale);

    return true;
  }
  Future<void> setSchoolYearId(int id) async {
    await PreferencesUtils.setInt(PreferencesKeys.schoolYearID.name, id);
  }

  Future<int?> getSchoolYearId() async {
    return await PreferencesUtils.getInt(PreferencesKeys.schoolYearID.name);
  }
  Future<void> setOnBoardingShown() async {
    await PreferencesUtils.setBool(PreferencesKeys.onBoardingShow.name, true);
  }

  Future<bool> isOnBoardingShown() async {
    return await PreferencesUtils.getBool(PreferencesKeys.onBoardingShow.name);
  }


  Future<bool> setLocale(String data) async {
    return await PreferencesUtils.setString(PreferencesKeys.lang.name, data);
  }

  Future<String?> getLocale() async {
    return await PreferencesUtils.getString(PreferencesKeys.lang.name);
  }

  Future<void> setLoggedIn() async {
    await PreferencesUtils.setBool(PreferencesKeys.isLoggedIn.name, true);
  }

  Future<void> setLogout() async {
    await PreferencesUtils.setBool(PreferencesKeys.isLoggedIn.name, false);
  }

  Future<bool> isLoggedIn() async {
    return await PreferencesUtils.getBool(PreferencesKeys.isLoggedIn.name);
  }

  Future<void> setIsLocaleEmarat(bool data) async {
    await PreferencesUtils.setBool(PreferencesKeys.isLocaleEmarat.name, data);
  }

  Future<bool> isLocaleEmarat() async {
    return await PreferencesUtils.getBool(PreferencesKeys.isLocaleEmarat.name);
  }

  Future<void> setAccessToken(String accessToken) async {
    await PreferencesUtils.setString(
      PreferencesKeys.accessToken.name,
      accessToken,
    );
  }

  Future<String?> getAccessToken() async {
    return await PreferencesUtils.getString(PreferencesKeys.accessToken.name);
  }

  Future<String?> getRefreshToken() async {
    return await PreferencesUtils.getString(PreferencesKeys.refreshToken.name);
  }

  Future<String?> getUserId() async {
    return await PreferencesUtils.getString(PreferencesKeys.userId.name);
  }

  Future<void> setUserId(String userId) async {
    await PreferencesUtils.setString(PreferencesKeys.userId.name, userId);
  }

  Future<void> setEmail(String data) async {
    await PreferencesUtils.setString(PreferencesKeys.email.name, data);
  }

  Future<String?> getEmail() async {
    return await PreferencesUtils.getString(PreferencesKeys.email.name);
  }

  Future<void> setProfileImage(String data) async {
    await PreferencesUtils.setString(PreferencesKeys.profileImage.name, data);
  }

  Future<String?> getProfileImage() async {
    return await PreferencesUtils.getString(PreferencesKeys.profileImage.name);
  }

  Future<String?> getPhone() async {
    return await PreferencesUtils.getString(PreferencesKeys.phone.name);
  }

  Future<void> setPhone(String phone) async {
    await PreferencesUtils.setString(PreferencesKeys.phone.name, phone);
  }

  Future<void> setIsGuest() async {
    await PreferencesUtils.setBool(PreferencesKeys.isGuest.name, true);
  }

  Future<bool> isGuest() async {
    return await PreferencesUtils.getBool(PreferencesKeys.isGuest.name);
  }

  Future<void> setUserFullNameEn(String data) async {
    await PreferencesUtils.setString(PreferencesKeys.userFullNameEn.name, data);
  }

  Future<void> setUserFullNameAr(String data) async {
    await PreferencesUtils.setString(PreferencesKeys.userFullNameAr.name, data);
  }

  Future<String?> getUserFullNameEn() async {
    return await PreferencesUtils.getString(
      PreferencesKeys.userFullNameEn.name,
    );
  }

  Future<void> setUserPosition(String data) async {
    await PreferencesUtils.setString(PreferencesKeys.position.name, data);
  }

  Future<String?> getUserPosition() async {
    return await PreferencesUtils.getString(PreferencesKeys.position.name);
  }

  Future<String?> getUserFullNameAr() async {
    return await PreferencesUtils.getString(
      PreferencesKeys.userFullNameAr.name,
    );
  }

  // Save User object as JSON string

  Future<void> setUserObject(User user) async {
  final userJson = jsonEncode(user.toJson());
  await PreferencesUtils.setString(PreferencesKeys.userObject.name, userJson);
}

  // Get User object from JSON string
  Future<User?> getUserObject() async {
    final userJsonString = await PreferencesUtils.getString(PreferencesKeys.userObject.name);
    if (userJsonString == null) return null;

    try {
      final userJson = jsonDecode(userJsonString) as Map<String, dynamic>;
      return User.fromJson(userJson);
    } catch (e) {
      return null;
    }
  }
}
