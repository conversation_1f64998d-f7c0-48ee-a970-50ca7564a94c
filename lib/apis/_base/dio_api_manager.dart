import 'dart:io';
import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:daleel/apis/_base/request_form_data_interceptor.dart';
import 'package:daleel/apis/api_keys.dart';
import 'package:daleel/apis/api_urls.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/preferences/preferences_manager.dart';
import 'package:daleel/utils/build_type/build_type.dart';
import 'package:daleel/utils/locale/locale_cubit.dart';
// import 'package:requests_inspector/requests_inspector.dart';
import 'package:daleel/utils/extensions/extension_string.dart';

class DioApiManager {
  final String defaultLanguage = "ar-EG";
  final PreferencesManager preferenceManager;
  final VoidCallback? logOutCallBack;

  //  dio instance to request token
  DioApiManager({
    required this.preferenceManager,
    this.logOutCallBack,
  });

  final _logInterceptor = AwesomeDioInterceptor();

  // final _requestsInspectorInterceptor = RequestsInspectorInterceptor();

  Dio get dioUnauthorized {
    return DioOptions.dioInstance(options)
      ..interceptors.clear()
      ..interceptors.addAll(
        [
          // _requestsInspectorInterceptor,
          _queuedInterceptorsWrapperUnauthorized,
          if (isDebugMode()) ...[_logInterceptor, RequestFormDataInterceptor()]
        ],
      );
  }

  Dio get dio {
    return DioOptions.dioInstance(options)
      ..interceptors.clear()
      ..interceptors.addAll(
        [
          // _requestsInspectorInterceptor,
          _queuedInterceptorsWrapper,
          if (isDebugMode()) ...[_logInterceptor, RequestFormDataInterceptor()]
        ],
      );
  }

  QueuedInterceptorsWrapper get _queuedInterceptorsWrapper {
    return QueuedInterceptorsWrapper(
      onError: _onError,
      onRequest: (request, handler) async {
        await _setLanguage(request);
        await _setToken(request);
        return handler.next(request);
      },
    );
  }

  QueuedInterceptorsWrapper get _queuedInterceptorsWrapperUnauthorized {
    return QueuedInterceptorsWrapper(
      onRequest: (request, handler) async {
        await _setLanguage(request);
        return handler.next(request);
      },
    );
  }

  Future<void> logOutNow() async {
    preferenceManager.clearData();
    logOutCallBack?.call();
  }

  Future<void> _setToken(RequestOptions request) async {
    final String? token = GetIt.I<UserMangers>().token;
    if (token.isNotNullOrNotEmpty) {
      if (request.headers[ApiKeys.authorization] == null) {
        request.headers[ApiKeys.authorization] = '${ApiKeys.keyBearer} $token';
      } else {
        request.headers.remove(ApiKeys.authorization);
      }
    }
  }

  DioOptions get options => DioOptions();

  Future<bool> updateHeaders() async {
    DioOptions.token = await preferenceManager.getAccessToken();
    DioOptions.language = await preferenceManager.getLocale();
    return true;
  }

  Future<void> _setLanguage(RequestOptions request) async {
    final String language = LocaleApp.mapFromString(
            await preferenceManager.getLocale() ?? defaultLanguage)
        .mapToPreferenceKey();
    if (request.headers[ApiKeys.locale] != language) {
      request.headers[ApiKeys.locale] = language == 'ar' ? 'ar-EG' : language;
    }
  }

  void _onError(DioException error, ErrorInterceptorHandler handler) async {
    if (error.response?.statusCode == 401 ||
        error.response?.statusCode == 403) {
      try {
        GetIt.I<UserMangers>().signOut();
      } catch (e) {
        return handler.reject(error);
      }
    }
    return handler.next(error);
  }
}

class DioOptions extends BaseOptions {
  @override
  Map<String, dynamic> get headers {
    final Map<String, dynamic> header = {};
    header.putIfAbsent(ApiKeys.locale, () => language);
    header.putIfAbsent(ApiKeys.accept, () => ApiKeys.applicationJson);
    header.putIfAbsent(ApiKeys.platform, () => Platform.isIOS ? 1 : 2);
    header.putIfAbsent('Application', () => 2);
    header.putIfAbsent('Time-Zone', () => timeZoneOffsetValue);

    return header;
  }

  @override
  String get baseUrl => ApiUrls.baseUrl;

  static String? token;
  static String? language;

  static Dio? dio;

  static Dio dioInstance(BaseOptions options) {
    dio ??= Dio(options);
    return dio!;
  }

  String get timeZoneOffsetValue {
    final offset = DateTime.now().timeZoneOffset.inMinutes;
    return '$offset';
  }
}
