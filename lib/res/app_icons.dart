import 'package:flutter/material.dart';

class AppIcons {
  AppIcons._();

  // ignore: unused_field
  static const String? _fontPackageKey = null;
  static const _filterIconKey = 'FilterIcon';
  static const _actionsIconKey = 'ActionsIcon';


  static const IconData filter = IconData(0xe800, fontFamily: _filterIconKey, fontPackage: _fontPackageKey);
  static const IconData edit = IconData(0xe800, fontFamily: _actionsIconKey, fontPackage: _fontPackageKey);
  static const IconData delete = IconData(0xe801, fontFamily: _actionsIconKey, fontPackage: _fontPackageKey);
}

/// generator helper website
/// https://www.fluttericon.com/
