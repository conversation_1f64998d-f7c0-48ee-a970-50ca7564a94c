import 'package:flutter/material.dart';

/// to control all colors, app theme, without any need to dig into code
/// any new color or existing color will have a const with its value
/// there is a stand alone variable for any widget, text, image or icon

abstract final class AppColors {
  const AppColors._();
  static const Color _black = Colors.black;
  static const Color _white = Colors.white;
  static const Color _transparent = Colors.transparent;
  static const Color _red = Colors.red;
  static const Color _jaffa = Color(0xFFF47B3D);
  static const Color _alto = Color(0xffD9D9D9);
  static const Color _checkboxBorder = Color(0xffADB7C2);
  static const Color _yellowOrange = Color(0xffFAAF40);
  static const Color _frenchGray = Color(0xffCFCFD0);
  static const Color _silver = Color(0xFF5F6263);
  static const Color _gray = Color(0xff929292);
  static const Color _mineShaft = Color(0xff333333);
  static const Color _dustyGray = Color(0xff9C9C9C);
  static const Color _cararra = Color(0xffF1F2EC);
  static const Color _wildSand = Color(0xFFF5F5F5);
  static const Color _greenHaze = Color(0xff009444);
  static const Color _orange_avater = Color(0xffFF624D);
  static const Color _background_color = Color(0xfff7f7f7);

  static const Color _amaranth = Color(0xffF03A47);
  static const Color _shark = Color(0xff1B1D21);
  static const Color _doveGray = Color(0xff737373);
  static const Color _porcelain = Color(0xffF4F6F7);
  static const Color _zircon = Color(0xFFEAEEF9);
  static const Color _monza = Color(0xffDD0000);
  static const Color _sapphire = Color(0xff2E50A3);
  static const Color _congressBlue = Color(0xff024C8E);
  static const Color _lightGray = Color(0xffA5A5A5);
  static const Color _lightPurple = Color(0xff2E50A3);
  static const Color _lightGreen = Color(0xff92BE33);
  static const Color _lightoffWhite = Color(0xffF8F8F8);
  static const Color _lightblue = Color(0xffAEBCDF);
  static const Color _main_gray = Color(0xffA6A6A6);
  static const Color _gray_subtitle = Color(0xff8D8D8D);
  static const Color _lightGraySubtitle = Color(0xFF919191);
  static const Color _blueGradient = Color(0xFFd0ebf7);
  static const Color _blueShadow = Color(0xFF42B4E3);
  static const Color _uploadmedia = Color(0xFFF3F3F5);
  static const Color _dividerColor = Color(0xffEEEEEE);
  /// app main theme ...
  static const colorSchemeSeed = _monza;
  static const colorDivider = _dividerColor;

  static const colorCertificateBackground = _lightoffWhite;
  static const colorshowAll = _lightblue;
  static const colorPrimary = _sapphire;
  static const colorHomeButtons = _lightPurple;
  static const colorHomeGreenButton = _lightGreen;
  static const colorSecondary = _congressBlue;
  static const scaffoldBackground = _white;
  static const appBarBackground = colorSecondary;
  static const transparentColor = _transparent;
  static const buttonBackground = colorSecondary;
  static const verifyEmailText = colorPrimary;
  static const statusHome = _blueShadow;
  static const backgroundUploadMedia = _uploadmedia;
  static const verifyEmailDescriptionText = _black;
  static const dropDownButtonColor = _black;
  static const titleColor = _white;
  static const containerColor = _white;
  static const forgetPassTitle = _lightGray;
  static const buttonWhiteTextColor = _white;
  static const buttonBlackTextColor = _black;
  static const leadingButtonBackgroundColor = _white;
  static const leadingButtonColor = _black;
  static const iconTheme = _jaffa;
  static const dotsIndicator = _jaffa;
  static const dotsIndicatorActive = _jaffa;
  static const floatActionBtnBackgroundColor = colorSecondary;
  static const tagGreenColor = _greenHaze;
  static const floatActionBtnForegroundColor = _white;
  static const bottomNavigationBarBackground = _white;
  static const appBarTextColor = _gray;
  static const appBarIconColor = _white;
  static const expandedDropDownColor = _yellowOrange;
  static const dropDownText = colorPrimary;
  static const selectedNavBarItem = colorPrimary;
  static const unSelectedNavBarItem = _alto;
  static const logOutButtonColor = _red;
  static const redButtonColor = _red;
  static const filterBorder = _black;
  static const filterIcon = _black;
  static const outlineButtonBorder = _black;
  static const whiteIcon = _white;
  static const mainGray = _main_gray;
  static const whiteText = _white;
  static const blueGradient = _blueGradient;
  static const orangeAvatar = _orange_avater;
  static const backgroundColor = _background_color;
  /// text colors
  static const bodyMedium = _shark;
  static const titleMedium = _white;
  static const headlineMedium = _white;
  static const bodySmall = _silver;
  static const titleSmall = _gray;
  static const headlineSmall = _dustyGray;
  static const labelSmall = _dustyGray;
  static const labelMedium = _doveGray;
  static const displaySmall = _amaranth;
  static const headlineLarge = _mineShaft;
  static const bodyLarge = _white;
  static const subTitleGray = _gray_subtitle;
  static const lightSubtitleGray = _lightGraySubtitle;

  /// app form field
  static const appDropdownButtonFormFieldFill = _black;
  static const appFormFieldFill = _wildSand;
  static const enabledAppFormFieldBorder = _cararra;
  static const suffixIcon = _silver;
  static const focusIcon = _yellowOrange;
  static const formFieldText = _white;
  static const formFieldProfileEnableBorder = _frenchGray;
  static const formFieldProfileFocusIBorder = _yellowOrange;
  static const formFieldProfileErrorIBorder = _red;
  static const formFieldFocusIBorder = colorPrimary;
  static const formFieldHintText = _gray;
  static const formFieldTitle = _mineShaft;
  static const filterTitles = _mineShaft;
  static const formFieldBorder = _alto;
  static const phoneNumberBorder = _alto;
  static const checkBoxFormFieldBg = _white;
  static const checkBoxFormFieldSelectedBg = colorPrimary;
  static const formImageFieldBorder = _gray;
  static const formImageSourceBorder = _zircon;
  static const formFieldAsterisk = _red;
  static const checkboxBorderColor = _checkboxBorder;

  static const viewFileWidgetBg = _porcelain;
  static const emptyRadioFormFelid = _porcelain;

  /// toast ..
  static const toastBackground = _black;
  static const toastText = _white;
  static const saveTxt = _white;

  static var splashBackground = _white;
}

///
/// All name colors according to https://chir.ag/projects/name-that-color
///   100% - FF
///   95% - F2
///   90% - E6
///   85% - D9
///   80% - CC
///   75% - BF
///   70% - B3
///   65% - A6
///   60% - 99
///   55% - 8C
///    50% - 80
///    45% - 73
///    40% - 66
///    35% - 59
///   30% - 4D
///   25% - 40
///   20% - 33
///   15% - 26
///   10% - 1A
///    5% - 0D
///   0% - 00
