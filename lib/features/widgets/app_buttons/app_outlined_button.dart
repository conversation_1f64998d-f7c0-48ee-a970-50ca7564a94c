import 'package:flutter/material.dart';
import 'package:daleel/features/widgets/app_buttons/common_widget.dart';

class AppOutlinedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final Color? textColor;
  final Color? borderColor;
  final EdgeInsets? padding;
  final double? borderRadius;
  final Color? backgroundColor;
  final double? width;

  const AppOutlinedButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.padding,
    this.textColor,
    this.borderColor,
    this.borderRadius,
    this.backgroundColor,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          side: BorderSide(
            color: borderColor ?? Theme.of(context).primaryColor,
            width: 1,
          ),
          shape: buttonShape,
          padding: padding ?? buttonPadding,
        ),
        onPressed: onPressed,
        child: child);
  }
}
