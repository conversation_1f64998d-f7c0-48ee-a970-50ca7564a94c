import 'package:flutter/material.dart';
import 'package:daleel/core/extensions/extension_theme.dart';
import 'package:daleel/res/app_colors.dart';

class LabelWithAsterisk extends StatelessWidget {
  final String text;
  final String? subtitle;
  final bool showAsterisk;

  const LabelWithAsterisk({
    super.key,
    required this.text,
    this.subtitle,
    this.showAsterisk = false,
  });

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: text,
            style: context.bodyMedium.copyWith(fontSize: 12),
          ),
          if (subtitle != null) ...[
            TextSpan(
              text: " ",
              style: context.bodyMedium.copyWith(fontSize: 12),
            ),
            TextSpan(
              text: subtitle,
              style: TextStyle(
                color: AppColors.lightSubtitleGray,
                fontSize: 10,
                fontWeight: FontWeight.w400,
                height: 0,
              ),
            ),
          ],
          if (showAsterisk)
            TextSpan(
              children: [
                TextSpan(
                  text: ' *',
                  style: context.bodyMedium.copyWith(
                    color: AppColors.formFieldAsterisk,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
