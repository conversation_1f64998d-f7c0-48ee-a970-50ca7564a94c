import 'package:daleel/core/widgets/base_stateful_screen_widget.dart';
import 'package:daleel/features/widgets/page_header/page_header.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AppWebViewWidget extends BaseStatefulScreenWidget {
  final String url;
  final String title;
  final bool showHeader;

  const AppWebViewWidget({
    super.key,
    required this.url,
    required this.title,
    this.showHeader = true,
  });

  @override
  BaseScreenState<AppWebViewWidget> baseScreenCreateState() =>
      _AppWebViewWidgetState();
}

class _AppWebViewWidgetState extends BaseScreenState<AppWebViewWidget> {
  late WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading progress if needed
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            hideLoading();
          },
          onHttpError: (HttpResponseError error) {
            hideLoading();
            setState(() {
              _isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            hideLoading();
            setState(() {
              _isLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));

    // Show loading initially
    showLoading();
  }

  @override
  Widget baseScreenBuild(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          if (widget.showHeader) PageHeader(title: widget.title),
          Expanded(
            child: Stack(
              children: [
                WebViewWidget(controller: _controller),
                if (_isLoading)
                  Container(
                    color: AppColors.scaffoldBackground,
                    child: Center(
                      child: CircularProgressIndicator(
                        color: AppColors.colorSecondary,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
