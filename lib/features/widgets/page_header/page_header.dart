import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:flutter/material.dart';

class PageHeader extends StatefulWidget implements PreferredSizeWidget {
  final String title;

  const PageHeader({super.key, required this.title});

  @override
  State<PageHeader> createState() => _PageHeaderState();

  // هنا بنحدد ارتفاع الـ AppBar
  @override
  Size get preferredSize => const Size.fromHeight(180);
}

class _PageHeaderState extends State<PageHeader> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(40),
          bottomRight: Radius.circular(40),
        ),
        image: DecorationImage(
          image: AssetImage(AppAssetPaths.backgroundHeader),
          fit: BoxFit.cover,
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Padding(
        padding: const EdgeInsets.only(top: 40),
        child: Row(
          children: [
            Text(
              widget.title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            Spacer(),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildIcon(Icons.calendar_month),
                const SizedBox(width: 10),
                _buildIcon(Icons.language),
                const SizedBox(width: 10),
                _buildIcon(Icons.notifications),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIcon(IconData icon) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.whiteIcon.withOpacity(0.1),
      ),
      padding: const EdgeInsets.all(10),
      child: Icon(icon, color: Colors.white),
    );
  }
}
