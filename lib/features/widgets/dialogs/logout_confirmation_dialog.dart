import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class LogoutConfirmationDialog extends StatelessWidget {
  final VoidCallback onConfirm;

  const LogoutConfirmationDialog({super.key, required this.onConfirm});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.85,
            minWidth: 280,
          ),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.whiteIcon,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppColors.bodyMedium.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Help icon from assets
              SizedBox(
                width: 100,
                height: 100,
                child: Image.asset(AppAssetPaths.ic_help),
              ),

              SizedBox(height: 24.h),

              // Question text
              Text(
                context.translate(LocalizationKeys.logoutConfirmation),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: AppColors.appBarBackground,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 32.h),

              // Confirm button
              _buildConfirmButton(context),

              SizedBox(height: 12.h),

              // Cancel button
              _buildCancelButton(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: ElevatedButton(
        onPressed: () {
          // context.pop();
          onConfirm();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.colorSecondary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 2,
        ),
        child: Text(
          context.translate(LocalizationKeys.confirm),
          style: TextStyle(
            color: AppColors.whiteIcon,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: OutlinedButton(
        onPressed: () {
          context.pop();
        },
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: AppColors.colorSecondary, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        child: Text(
          context.translate(LocalizationKeys.cancel),
          style: TextStyle(
            color: AppColors.colorSecondary,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  static Future<void> show(BuildContext context, VoidCallback onConfirm) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      builder:
          (context) => Material(
            type: MaterialType.transparency,
            child: LogoutConfirmationDialog(onConfirm: onConfirm),
          ),
    );
  }
}
