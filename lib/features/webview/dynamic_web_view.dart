import 'dart:collection';
import 'package:daleel/core/extensions/webview_extensions.dart';
import 'package:daleel/features/webview/web_view_auth_scripts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get_it/get_it.dart';
import '../../preferences/preferences_manager.dart';
import '../../features/login/data/models/login_response_api_model.dart';

class DynamicWebView extends StatefulWidget {
  final String url;
  final String redirectUrl;
  final Function? onSuccessCallback;
  final Function? onFailureCallback;
  final Function? onLoadStart;
  final Function? onLoadStop;
  final UnmodifiableListView<UserScript>? initialUserScripts;

  const DynamicWebView({
    required this.url,
    required this.redirectUrl,
    this.onSuccessCallback,
    this.onFailureCallback,
    this.onLoadStart,
    this.onLoadStop,
    this.initialUserScripts,
    super.key,
  });

  @override
  State<DynamicWebView> createState() => _DynamicWebViewState();
}

class _DynamicWebViewState extends State<DynamicWebView> {
  String? _prefToken;
  String? _prefLanguage;
  User? _prefUser;
  List<String>? _prefClaims;
  bool _isLoading = true;
  @override
  void initState() {
    super.initState();
    _loadPrefs();
  }

  Future<void> _loadPrefs() async {
    try {
      final pm = GetIt.I<PreferencesManager>();
      _prefToken = await pm.getAccessToken() ?? '';
      _prefLanguage = await pm.getLocale() ?? 'en';
      _prefUser = await pm.getUserObject();
      _prefClaims = _prefUser?.claims ?? [];
      debugPrint("Token: $_prefToken");
      debugPrint("Language: $_prefLanguage");
      debugPrint("User: ${_prefUser?.toWebViewJson()}");
      debugPrint("Claims: $_prefClaims");
    } catch (e) {
      _prefToken = '';
      _prefLanguage = 'en';
      _prefUser = null;
      _prefClaims = [];
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _clearEverything() async {
    await InAppWebViewController.clearAllCache();
    await CookieManager.instance().deleteAllCookies();

    try {
      await WebStorageManager.instance().deleteAllData();
    } catch (e) {
      debugPrint(
        "WebStorageManager.deleteAllData() not supported on this platform: $e",
      );
    }
  }

  Future<void> _clearWebViewCache() async {
    await InAppWebViewController.clearAllCache();
    await CookieManager.instance().deleteAllCookies();
    debugPrint("WebView cache and cookies cleared.");
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Stack(
          children: [
            if (!_isLoading)
              InAppWebView(
                onWebViewCreated: (controller) async {
                  await _clearEverything();
                  await _clearWebViewCache();
                },

                initialUserScripts:
                    widget.initialUserScripts ??
                    WebViewAuthScripts.buildInitialUserScripts(
                      token: _prefToken ?? '',
                      language: _prefLanguage ?? 'en',
                      user: _prefUser?.toWebViewJson() ,
                      claims: _prefClaims?.toWebViewJson(),
                    ),

                initialUrlRequest: URLRequest(url: WebUri(widget.url)),

                initialSettings: InAppWebViewSettings(
                  javaScriptEnabled: true,
                  mediaPlaybackRequiresUserGesture: false,
                  cacheEnabled: false,
                  cacheMode: CacheMode.LOAD_NO_CACHE,
                  clearCache: true,
                  allowFileAccessFromFileURLs: true,
                  allowUniversalAccessFromFileURLs: true,
                  useHybridComposition: true,
                  allowFileAccess: true,
                  domStorageEnabled: true,
                  databaseEnabled: true,
                  builtInZoomControls: false,
                  allowsInlineMediaPlayback: true,
                  sharedCookiesEnabled: true,
                  incognito: true,
                ),

                onLoadStart: (controller, url) async {
                  widget.onLoadStart?.call();
                },

                onLoadStop: (controller, url) async {
                  widget.onLoadStop?.call();
                  final current = url?.toString() ?? "";
                  if (current == widget.redirectUrl) {
                    widget.onSuccessCallback?.call();
                    return;
                  }
                },

                onLoadResource: (controller, resource) {
                  final u = resource.url.toString();
                  if (u.contains("success")) {
                    widget.onSuccessCallback?.call();
                  } else if (u.contains("failure")) {
                    widget.onFailureCallback?.call();
                  }
                },

                shouldOverrideUrlLoading: (controller, navAction) async {
                  final u = navAction.request.url?.toString() ?? "";
                  if (u.contains("success")) {
                    widget.onSuccessCallback?.call();
                    return NavigationActionPolicy.CANCEL;
                  } else if (u.contains("failure")) {
                    widget.onFailureCallback?.call();
                    return NavigationActionPolicy.CANCEL;
                  }
                  return NavigationActionPolicy.ALLOW;
                },

                onReceivedError: (controller, request, error) {
                  widget.onFailureCallback?.call();
                },

                onReceivedHttpError: (controller, request, errorResponse) {
                  widget.onFailureCallback?.call();
                },
                onConsoleMessage: (controller, consoleMessage) {
                  debugPrint(
                    "Console: ${consoleMessage.messageLevel} - ${consoleMessage.message}",
                  );
                },
              ),
            if (_isLoading) const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }
}
