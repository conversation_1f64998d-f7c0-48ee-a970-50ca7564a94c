import 'dart:collection';
import 'dart:convert';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class WebViewAuthScripts {
  WebViewAuthScripts._();

  static String bootstrapAuthJs({
    required String? token,
    required String? language,
    Map<String, dynamic>? claims,
    Map<String, dynamic>? user,
    String platform = 'mobileAndroid',
  }) {
    final claimsJson = jsonEncode(
      claims ?? const {"S_MenuItem_user": "S_MenuItem_user"},
    );
    final userJson = jsonEncode(user ?? const {"id": 123, "name": "<PERSON>"});

    return '''
(function() {
  try {
    const storageData = {
      "\$AJ\$token": "${token ?? ''}",
      "preferredLanguage": "${language ?? 'en'}",
      "\$AJ\$claims": JSON.stringify($claimsJson),
      "\$AJ\$user": JSON.stringify($userJson),
      "platform": "$platform"
    };

    localStorage.clear();
    sessionStorage.clear();

    for (const key in storageData) {
      localStorage.setItem(key, storageData[key]);
    }
  } catch (e) {
    console.error("Auth bootstrap error:", e);
  }
})();
''';
  }

  static UnmodifiableListView<UserScript> buildInitialUserScripts({
    required String? token,
    required String? language,
    Map<String, dynamic>? claims,
    Map<String, dynamic>? user,
    String platform = 'mobileAndroid',
    UserScriptInjectionTime injectionTime =
        UserScriptInjectionTime.AT_DOCUMENT_START,
  }) {
    return UnmodifiableListView<UserScript>([
      UserScript(
        source: bootstrapAuthJs(
          token: token,
          language: language,
          claims: {
            "claims": [
              "S_MenuItem_user",
              "S_MenuItem_Request",
              "G_CertificateIssuranceRequest",
              "GSE_U_ChildInfo",
              "G_DeleteChild",
              "GSE_R_ChildInfo",
              "GSE_R_ChildRequests",
              "R_U_DivisionInfo",
              "E_U_DivisionTeachers",
              "E_R_DivisionTeachers",
              "E_R_DivisionStudents",
              "E_TransferStudentToAnotherDivision",
              "E_U_StudentTrack",
              "E_C_DivisionStudents",
              "E_RaiseDivisionAbsenceRecord",
              "E_R_DivisionAbsenceRecord",
              "E_D_AbsentStudent",
              "E_RaiseDivisionDegrees",
              "E_R_DivisionSubjects",
              "E_AcceptOrRejectSubjectDegress",
              "E_R_DivisionSubjectsDegrees",
              "E_R_DivisionDegrees",
              "E_R_DivisionStudentRate",
              "E_U_DivisionStudentRate",
              "E_U_StudentDegree",
              "S_UploadExam",
              "S_MenuItem_Exam",
              "E_MenuItem_Exams",
              "E_R_GradeInfo",
              "E_U_GradeInfo",
              "S_MenuItem_GuardianReport",
              "S_MenuItem_Holiday",
              "E_EditFlexableHoliday",
              "E_MenuItem_AnnualHolidays",
              "S_C_Index",
              "S_MenuItem_Index",
              "S_U_Index",
              "E_C_GradeLecture",
              "E_U_GradeLecture",
              "E_D_GradeLecture",
              "E_R_GradeLecture",
              "E_U_DivisionLecuture",
              "E_R_DivisionLecuture",
              "E_MenuItem_Evaluation",
              "S_AddEvaluation",
              "S_EditEvaluation",
              "S_DeleteEvaluation",
              "S_MenuItem_Role",
              "S_MenuItem_SchoolMenu",
              "E_MenuItem_SchoolEmployee",
              "E_MenuItem_SchoolGrades",
              "E_MenuItem_Subjects",
              "E_MenuItem_SchoolDivisions",
              "S_MenuItem_SchoolYear",
              "S_MenuItem_StudentMenu",
              "G_ExemptionFromStudySubjectReqest",
              "E_R_GradeStudents",
              "SEG_U_StudentInfo",
              "SEG_R_StudentInfo",
              "G_RepeatStudyPhaseRequest",
              "S_TransferStudentToAnotherSchool",
              "E_DeleteStudentRequest",
              "GS_RegisterChild",
              "SEG_U_StudentAttachments",
              "SEG_R_StudentAttachments",
              "E_TransferStudentGroup",
              "SEG_U_StudentMedicalFile",
              "SEG_R_StudentMedicalFile",
              "GE_ChangePersonalIdentityReqest",
              "SEG_R_StudentRecord",
              "E_MenuItem_Students",
              "SEG_R_StudentCertificates",
              "SEG_R_StudentAbsenceAndAttendance",
              "G_WithdrawingStudentFromCurrentSchool",
              "G_C_Child",
              "E_C_GradeSubject",
              "S_MenuItem_SubjectMenu",
              "S_MenuItem_Survey",
              "S_MenuItem_Setting",
              "S_NotificationSetting",
              "S_MenuItem_SchoolEmployeeReport",
              "S_MenuItem_SchoolReport",
              "S_MenuItem_SubjectReport",
              "S_MenuItem_SchoolTeacherReport",
              "S_MenuItem_StudentReport",
              "S_MenuItem_DegreesReport",
              "S_MenuItem_AbsenceReport",
              "S_D_Role",
              "S_C_Role",
              "S_U_Role",
              "S_C_SchoolYear",
              "S_U_SchoolYear",
              "S_SchoolYear_C_Curriculums",
              "S_SchoolYear_C_Grade",
              "S_SchoolYear_U_Grade",
              "S_C_Survey",
              "S_U_Survey",
              "G_Survey",
              "S_C_Subject",
              "S_U_Subject",
              "E_C_Subject",
              "S_C_User",
              "S_U_User",
              "S_C_Holiday",
              "S_U_Holiday",
              "S_D_Subject",
              "S_Menu_SchoolsAndStudents",
              "S_Menu_EducationalSetting",
              "S_Menu_ReportsManagement",
              "S_Menu_ManagarTools",
              "E_Acc_R_StudentPayments",
              "E_Acc_U_StudentPayments",
              "E_MenuItem_Requests",
              "S_MenuItem_GuardianMenu",
              "E_R_DivisionInfo",
              "E_MenuItem_parents",
              "S_UpdateStudentIdentity",
              "S_U_ProhibitedFromIssueeCertificateFromSpea",
              "S_U_ProhibitedFromWithdrawingFromSpea",
              "SE_U_ProhibitedFromIssueeCertificateFromSchool",
              "SE_U_ProhibitedFromWithdrawingFromSchool",
              "SEG_R_StudentAcceptanceInfo",
              "SEG_U_StudentAcceptanceInfo",
              "SEG_R_StudentBehavior",
              "SEG_R_StudentSubjectsAndDegrees",
              "Issue Student certificate From Spea Manually",
              "SE_MenuItem_EditList",
              "E_MenuItem_GeneralInfo",
              "SE_MenuItem_Exam",
              "E_RaseDivisionDegrees",
              "ManageWorkflow",
              "S_Menu_PeformanceManagment",
              "E_U_Subject",
              "S_MenuItem_TransferedStudentsReport",
              "S_U_UserPassword",
              "S_StudentCertificateIssue",
              "SE_TakeUndoAction",
              "SE_TakeUndoAction",
              "SE_U_SchoolEmployee",
              "GSE_R_StudentRequests",
              "SE_A_Division",
              "S_U_ProhibitedFromRegisteration",
              "S_U_GradingSystem",
              "G_U_UPdateGrade",
              "S_U_UPdateGrade",
              "S_U_UPdateDegreeAfterFinal",
              "S_S_SchoolStatstic",
              "S_S_StudentStatstic",
              "S_S_StudentApplityStatstic",
              "S_S_EmployeeStatstic",
              "S_E_UpgradeStudent",
              "S_UpgradeStudentButton",
              "S_EditSpeaSubject",
              "S_D_Division",
              "S_GetAllSchoolYearPeriod",
              "S_GetSchoolYearPeriodById",
              "S_AddSchoolYearPeriod",
              "S_EditSchoolYearPeriod",
              "S_DeleteSchoolYearPeriod",
              "S_GetSchoolYearPeriodBySchoolId",
              "S_GetAllSchoolYearWeekend",
              "S_GetSchoolYearWeekendById",
              "S_AddSchoolYearWeekend",
              "S_EditSchoolYearWeekend",
              "S_DeleteSchoolYearWeekend",
              "S_GetSchoolYearWeekendBySchoolId",
              "S_GetAllSchoolYearHoliday",
              "S_GetSchoolYearHolidayById",
              "S_AddSchoolYearHoliday",
              "S_EditSchoolYearHoliday",
              "S_DeleteSchoolYearHoliday",
              "S_GetSchoolYearHolidayBySchoolId",
              "S_GetAllSchoolYearEvent",
              "S_GetSchoolYearEventById",
              "S_AddSchoolYearEvent",
              "S_EditSchoolYearEvent",
              "S_DeleteSchoolYearEvent",
              "S_GetSchoolYearEventBySchoolId",
              "S_GetAllNumberOfHoliday",
              "S_GetNumberOfHolidayById",
              "S_AddNumberOfHoliday",
              "S_EditNumberOfHoliday",
              "S_DeleteNumberOfHoliday",
              "S_GetNumberOfHolidayBySchoolId",
              "S_MenuItem_AcademicCalendar",
              "S_GetAllSemester",
              "S_GetSemesterById",
              "S_EditSemester",
              "S_DeleteSemester",
              "S_GetSemesterBySchoolId",
              "S_AddSemester",
              "S_MenuItem_ViewAcademicCalendar",
              "D_GetAllDelegate",
              "D_DeleteDelegate",
              "D_AddDelegate",
              "D_UpdateDelegate",
              "S_E_ViewIsPaiedStatus",
              "E_AddSemesterLimit",
              "E_ShowSemesterLimit",
              "D_UpdateDisability",
              "S_E_ViewStudentProhibit_SPEA",
              "S_E_ViewStudentProhibit_School",
              "S_E_UpdateStudentProhibit_SPEA",
              "S_E_UpdateStudentProhibit_School",
              "S_B_DeleteStudentBehavior",
              "S_GetStudentSubjectExempt",
              "N_GetNewsTicker",
            ],
          },
          user: user,
          platform: platform,
        ),
        injectionTime: injectionTime,
      ),
    ]);
  }
}
