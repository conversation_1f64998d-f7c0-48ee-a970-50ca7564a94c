import 'dart:collection';
import 'dart:convert';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import '../../utils/platform/platform.dart';

class WebViewAuthScripts {
  WebViewAuthScripts._();

  static const String _kTokenKey = r'$AJ$token';
  static const String _kUserKey = r'$AJ$user';
  static const String _kClaimsKey = r'$AJ$claims';
  static const String _kLangKey = 'preferredLanguage';
  static const String _kPlatformKey = 'platform';

  static String iife(String js) => '(()=>{$js})();';

  static String bootstrapAuthJs({
    String? token,
    String? language,
    Map<String, dynamic>? claims,
    Map<String, dynamic>? user,
    String? platform,
    bool clearStorages = true,
    bool alsoSession = false,
  }) {
    final checkedPlatform = platform ?? getPlatform();
    final tokenJs = jsonEncode(token ?? '');
    final languageJs = jsonEncode(
      (language == null || language.isEmpty) ? 'en' : language,
    );
    final platformJs = jsonEncode(checkedPlatform);

    final claimsObjLiteral = jsonEncode(
      claims ?? const {"S_MenuItem_user": "S_MenuItem_user"},
    );
    final userObjLiteral = jsonEncode(
      user ?? const {"id": 123, "name": "Omar"},
    );

    final clearLocal = clearStorages ? 'localStorage.clear();' : '';
    final clearSession = (clearStorages && alsoSession) ? 'sessionStorage.clear();' : '';

    final js = '''
      try {
        $clearLocal
        $clearSession

        const data = {
          ${jsonEncode(_kTokenKey)}: $tokenJs,
          ${jsonEncode(_kLangKey)}: $languageJs,
          ${jsonEncode(_kClaimsKey)}: JSON.stringify($claimsObjLiteral),
          ${jsonEncode(_kUserKey)}: JSON.stringify($userObjLiteral),
          ${jsonEncode(_kPlatformKey)}: $platformJs
        };

        console.log("[WebView] Injecting auth data:", data);

        Object.entries(data).forEach(([k, v]) => {
          localStorage.setItem(k, String(v));
        });

        const dump = {};
        for (let i = 0; i < localStorage.length; i++) {
          const k = localStorage.key(i);
          dump[k] = localStorage.getItem(k);
        }
        console.log("[WebView] localStorage snapshot after injection:", dump);
      } catch (e) {
        console.error("[WebView] Auth bootstrap error:", e);
      }
    ''';

    return iife(js);
  }

  static String _postLoadLoggerJs() {
    return '''
      document.addEventListener('DOMContentLoaded', () => {
        try {
          const dump = {};
          for (let i = 0; i < localStorage.length; i++) {
            const k = localStorage.key(i);
            dump[k] = localStorage.getItem(k);
          }
          console.log("[WebView] DOMContentLoaded dump:", dump);
        } catch (e) {
          console.error("[WebView] dump error:", e);
        }
      });
    ''';
  }

  static UnmodifiableListView<UserScript> buildInitialUserScripts({
    String? token,
    String? language,
    Map<String, dynamic>? claims,
    Map<String, dynamic>? user,
    String? platform,
    bool clearStorages = true,
    bool alsoSession = false,
    UserScriptInjectionTime injectionTime = UserScriptInjectionTime.AT_DOCUMENT_START,
    bool forMainFrameOnly = true,
    bool enableDebugLogs = true,
  }) {
    final source = bootstrapAuthJs(
      token: token,
      language: language,
      claims: claims,
      user: user,
      platform: platform,
      clearStorages: clearStorages,
      alsoSession: alsoSession,
    );

    final scripts = <UserScript>[
      UserScript(
        source: source,
        injectionTime: injectionTime,
        forMainFrameOnly: forMainFrameOnly,
      ),
    ];

    if (enableDebugLogs) {
      scripts.add(
        UserScript(
          source: _postLoadLoggerJs(),
          injectionTime: UserScriptInjectionTime.AT_DOCUMENT_END,
          forMainFrameOnly: forMainFrameOnly,
        ),
      );
    }
    return UnmodifiableListView<UserScript>(scripts);
  }
}

