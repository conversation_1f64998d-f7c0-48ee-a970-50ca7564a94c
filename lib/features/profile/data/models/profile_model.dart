class ProfileModel {
  final String fullName;
  final String arabicFullName;
  final String email;
  final String phoneNumber;
  final String? photoPath;
  final String nationality;
  final String gender;
  final DateTime registrationDate;
  final int userId;
  final String position;

  ProfileModel({
    required this.fullName,
    required this.arabicFullName,
    required this.email,
    required this.phoneNumber,
    this.photoPath,
    required this.nationality,
    required this.gender,
    required this.registrationDate,
    required this.userId,
    this.position = '',
  });

  factory ProfileModel.fromJson(Map<String, dynamic> json) {
    return ProfileModel(
      fullName: json['fullName'] as String? ?? '',
      arabicFullName: json['arabicFullName'] as String? ?? '',
      email: json['email'] as String? ?? '',
      phoneNumber: json['phoneNumber'] as String? ?? '',
      photoPath: json['photoPath'] as String?,
      nationality: json['nationality'] as String? ?? '',
      gender: json['gender'] as String? ?? '',
      registrationDate:
          json['registrationDate'] != null
              ? DateTime.parse(json['registrationDate'])
              : DateTime.now(),
      userId: json['userId'] as int? ?? 0,
      position: json['position'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'arabicFullName': arabicFullName,
      'email': email,
      'phoneNumber': phoneNumber,
      'photoPath': photoPath,
      'nationality': nationality,
      'gender': gender,
      'registrationDate': registrationDate.toIso8601String(),
      'userId': userId,
      'position': position,
    };
  }

  ProfileModel copyWith({
    String? fullName,
    String? arabicFullName,
    String? email,
    String? phoneNumber,
    String? photoPath,
    String? nationality,
    String? gender,
    DateTime? registrationDate,
    int? userId,
    String? position,
  }) {
    return ProfileModel(
      fullName: fullName ?? this.fullName,
      arabicFullName: arabicFullName ?? this.arabicFullName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      photoPath: photoPath ?? this.photoPath,
      nationality: nationality ?? this.nationality,
      gender: gender ?? this.gender,
      registrationDate: registrationDate ?? this.registrationDate,
      userId: userId ?? this.userId,
      position: position ?? this.position,
    );
  }

  // Helper method to get name based on locale
  String getName(bool isEnglish) {
    return isEnglish ? fullName : arabicFullName;
  }

  // Helper method to format registration date
  String getFormattedRegistrationDate() {
    return '${registrationDate.day.toString().padLeft(2, '0')}/${registrationDate.month.toString().padLeft(2, '0')}/${registrationDate.year}';
  }
}
