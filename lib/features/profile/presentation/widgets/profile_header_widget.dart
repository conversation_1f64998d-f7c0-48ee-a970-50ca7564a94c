import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';

class ProfileHeaderWidget extends StatelessWidget {
  final UserMangers userManager;

  const ProfileHeaderWidget({super.key, required this.userManager});

  @override
  Widget build(BuildContext context) {
    final isEnglish = context.isEnglish;
    final userName = userManager.getUserName(isEnglish);
    final userEmail = userManager.getEmail();
    final userImage = userManager.getImageUrl();

    return Row(
      children: [
        // Profile Image
        CircleAvatar(
          radius: 35,
          backgroundImage:
              userImage.isNotEmpty && userImage != 'null'
                  ? NetworkImage(userImage)
                  : const AssetImage(AppAssetPaths.ic_user_icon)
                      as ImageProvider,
        ),

        SizedBox(width: 16.w),

        // Profile Info
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                userName.isNotEmpty
                    ? userName
                    : context.translate(LocalizationKeys.defaultProfileName),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.appBarBackground,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
              SizedBox(height: 8.h),
              Text(
                userEmail.isNotEmpty
                    ? userEmail
                    : context.translate(LocalizationKeys.defaultEmail),
                style: TextStyle(fontSize: 14, color: AppColors.subTitleGray),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ],
          ),
        ),

        // Edit Icon
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.colorSecondary.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(Icons.edit, color: AppColors.colorSecondary, size: 20),
        ),
      ],
    );
  }
}
