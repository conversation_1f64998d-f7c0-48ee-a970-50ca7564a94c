import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';

class ProfileAppBar extends StatelessWidget {
  const ProfileAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180,
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.colorSecondary,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(40),
          bottomRight: Radius.circular(40),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Padding(
        padding: const EdgeInsets.only(top: 40),
        child: Row(
          children: [
            // Back Button
            GestureDetector(
              onTap: () {
                if (context.canPop()) {
                  context.pop();
                } else {
                  // If can't pop, go to main tabs (home screen)
                  context.go('/maintabs');
                }
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.whiteIcon.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.arrow_back,
                  color: AppColors.whiteIcon,
                  size: 20,
                ),
              ),
            ),

            const Spacer(),

            // Title
            Text(
              context.translate(LocalizationKeys.myProfile),
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.whiteIcon,
              ),
            ),

            const Spacer(),

            // Empty space for symmetry
            const SizedBox(width: 40),
          ],
        ),
      ),
    );
  }
}
