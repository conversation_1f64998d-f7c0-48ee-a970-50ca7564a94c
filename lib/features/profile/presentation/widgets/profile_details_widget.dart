import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:daleel/res/app_colors.dart';

class ProfileDetailsWidget extends StatelessWidget {
  final UserMangers userManager;

  const ProfileDetailsWidget({super.key, required this.userManager});

  @override
  Widget build(BuildContext context) {
    final isEnglish = context.isEnglish;
    final userName = userManager.getUserName(isEnglish);
    final userPhone = userManager.getPhoneNumber();
    final userPosition = userManager.getPosition();
    final isEmirati = userManager.isLocaleEmarat();

    return Column(
      children: [
        _buildDetailRow(
          context,
          context.translate(LocalizationKeys.familyName),
          userName.isNotEmpty
              ? userName
              : context.translate(LocalizationKeys.defaultProfileName),
        ),
        SizedBox(height: 20.h),

        _buildDetailRow(
          context,
          context.translate(LocalizationKeys.nationality),
          isEmirati
              ? context.translate(LocalizationKeys.emirati)
              : context.translate(LocalizationKeys.emirati),
        ),
        SizedBox(height: 20.h),

        _buildDetailRow(
          context,
          context.translate(LocalizationKeys.gender),
          context.translate(LocalizationKeys.male),
        ),
        SizedBox(height: 20.h),

        _buildDetailRow(
          context,
          context.translate(LocalizationKeys.position),
          userPosition.isNotEmpty
              ? userPosition
              : context.translate(LocalizationKeys.guardian),
        ),
        SizedBox(height: 20.h),

        _buildDetailRow(
          context,
          context.translate(LocalizationKeys.mobileNumber),
          userPhone.isNotEmpty
              ? userPhone
              : context.translate(LocalizationKeys.defaultProfilePhone),
        ),
        SizedBox(height: 20.h),

        _buildDetailRow(
          context,
          context.translate(LocalizationKeys.phoneNumber),
          userPhone.isNotEmpty
              ? userPhone
              : context.translate(LocalizationKeys.defaultProfilePhone),
        ),
        SizedBox(height: 20.h),

        _buildDetailRow(
          context,
          context.translate(LocalizationKeys.registrationDate),
          context.translate(LocalizationKeys.defaultProfileDate),
        ),
      ],
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          flex: 2,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.subTitleGray,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        SizedBox(width: 8.w),
        Flexible(
          flex: 3,
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.appBarBackground,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.end,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }
}
