import 'package:daleel/features/profile/data/models/profile_model.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:get_it/get_it.dart';

part 'profile_event.dart';
part 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final UserMangers userManager = GetIt.I<UserMangers>();

  ProfileBloc() : super(ProfileInitialState()) {
    on<LoadProfileEvent>(_onLoadProfile);
    on<UpdateProfileEvent>(_onUpdateProfile);
    on<LogoutEvent>(_onLogout);
  }

  Future<void> _onLoadProfile(
    LoadProfileEvent event,
    Emitter<ProfileState> emit,
  ) async {
    emit(ProfileLoadingState());

    try {
      // Load user data from UserManager
      await userManager.setUserInfoAndModeFromLocal();

      // Get user data with fallbacks
      final englishName = userManager.getUserName(true);
      final arabicName = userManager.getUserName(false);
      final email = userManager.getEmail();
      final phoneNumber = userManager.getPhoneNumber();
      final imageUrl = userManager.getImageUrl();
      final userId = userManager.getUserId();
      final position = userManager.getPosition();
      final isLocaleEmarat = userManager.isLocaleEmarat();
      // Create ProfileModel from UserManager data with fallbacks
      final profile = ProfileModel(
        fullName: englishName.isNotEmpty ? englishName : 'محمد أحمد',
        arabicFullName: arabicName.isNotEmpty ? arabicName : 'محمد أحمد',
        email: email.isNotEmpty ? email : '<EMAIL>',
        phoneNumber: phoneNumber.isNotEmpty ? phoneNumber : '+97123546512',
        photoPath: imageUrl.isNotEmpty && imageUrl != 'null' ? imageUrl : null,
        nationality: isLocaleEmarat ? 'إماراتي' : 'إماراتي',
        gender: 'ذكر',
        registrationDate: DateTime(2025, 1, 22, 14, 0), // 22 يناير 2025 2:00p
        userId: int.tryParse(userId) ?? 0,
        position: position.isNotEmpty ? position : 'ولي أمر',
      );

      emit(ProfileLoadedState(profile: profile));
    } catch (e) {
      // Create fallback profile data
      final fallbackProfile = ProfileModel(
        fullName: 'محمد أحمد',
        arabicFullName: 'محمد أحمد',
        email: '<EMAIL>',
        phoneNumber: '+97123546512',
        photoPath: null,
        nationality: 'إماراتي',
        gender: 'ذكر',
        registrationDate: DateTime(2025, 1, 22, 14, 0),
        userId: 0,
        position: 'ولي أمر',
      );

      emit(ProfileLoadedState(profile: fallbackProfile));
    }
  }

  Future<void> _onUpdateProfile(
    UpdateProfileEvent event,
    Emitter<ProfileState> emit,
  ) async {
    if (state is ProfileLoadedState) {
      emit(ProfileLoadingState());

      try {
        // Here you would typically call an API to update the profile
        // For now, we'll just update the local state

        // Update UserManager with new data
        userManager.setUserName(
          userNameEn: event.profile.fullName,
          userNameAr: event.profile.arabicFullName,
        );
        userManager.setEmail(event.profile.email);
        userManager.setImageUrl(event.profile.photoPath ?? '');

        emit(ProfileLoadedState(profile: event.profile));
      } catch (e) {
        emit(ProfileErrorState(LocalizationKeys.somethingWentWrong, true));
      }
    }
  }

  Future<void> _onLogout(LogoutEvent event, Emitter<ProfileState> emit) async {
    try {
      await userManager.signOut();
      emit(ProfileLogoutSuccessState());
    } catch (e) {
      emit(ProfileErrorState(LocalizationKeys.somethingWentWrong, true));
    }
  }
}
