part of 'profile_bloc.dart';

abstract class ProfileState extends Equatable {
  const ProfileState();

  @override
  List<Object?> get props => [];
}

class ProfileInitialState extends ProfileState {}

class ProfileLoadingState extends ProfileState {}

class ProfileLoadedState extends ProfileState {
  final ProfileModel profile;

  const ProfileLoadedState({required this.profile});

  @override
  List<Object?> get props => [profile];

  ProfileLoadedState copyWith({
    ProfileModel? profile,
  }) {
    return ProfileLoadedState(
      profile: profile ?? this.profile,
    );
  }
}

class ProfileErrorState extends ProfileState {
  final String errorMessage;
  final bool isLocalizationKey;

  const ProfileErrorState(this.errorMessage, this.isLocalizationKey);

  @override
  List<Object> get props => [errorMessage, isLocalizationKey];
}

class ProfileLogoutSuccessState extends ProfileState {}
