import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/profile/data/models/profile_model.dart';
import 'package:daleel/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:daleel/features/profile/presentation/widgets/profile_app_bar.dart';
import 'package:daleel/features/profile/presentation/widgets/profile_details_widget.dart';
import 'package:daleel/features/profile/presentation/widgets/profile_header_widget.dart';
import 'package:daleel/features/widgets/dialogs/logout_confirmation_dialog.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:daleel/core/widgets/base_stateful_screen_widget.dart';
import 'package:daleel/utils/feedback/feedback_message.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';

class ProfileScreen extends StatelessWidget {
  static const String routeName = '/profile';

  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ProfileBloc>(
      create: (context) => ProfileBloc()..add(const LoadProfileEvent()),
      child: const ProfileScreenWithBloc(),
    );
  }
}

class ProfileScreenWithBloc extends BaseStatefulScreenWidget {
  const ProfileScreenWithBloc({super.key});

  @override
  BaseScreenState<ProfileScreenWithBloc> baseScreenCreateState() =>
      _ProfileScreenWithBlocState();
}

class _ProfileScreenWithBlocState
    extends BaseScreenState<ProfileScreenWithBloc> {
  ProfileBloc get currentBloc => context.read<ProfileBloc>();
  final UserMangers userManager = GetIt.I<UserMangers>();

  @override
  void initState() {
    super.initState();
    // Load user data when screen initializes
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    await userManager.setUserInfoAndModeFromLocal();
    setState(() {});
  }

  @override
  Widget baseScreenBuild(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<ProfileBloc, ProfileState>(
        listener: (context, state) {
          if (state is ProfileLoadingState) {
            showLoading();
          } else {
            hideLoading();
          }
          if (state is ProfileErrorState) {
            showFeedbackMessage(state.errorMessage);
          }
          if (state is ProfileLogoutSuccessState) {
            // Navigation will be handled by UserManager.signOut()
            // userManager.signOut();
          }
        },
        builder: (context, state) => _buildProfileWidget(state),
      ),
    );
  }

  Widget _buildProfileWidget(ProfileState state) {
    return Column(
      children: [
        // Header with back button
        const ProfileAppBar(),

        // Content
        Expanded(child: _buildProfileContent(state)),
      ],
    );
  }

  Widget _buildProfileContent(ProfileState state) {
    if (state is ProfileLoadedState) {
      return _buildLoadedContent(state.profile);
    }

    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildLoadedContent(ProfileModel profile) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          SizedBox(height: 20.h),

          // Profile Image and Name Section
          ProfileHeaderWidget(userManager: userManager),

          SizedBox(height: 40.h),

          // Profile Details
          ProfileDetailsWidget(userManager: userManager),

          SizedBox(height: 80.h),

          // Logout Button
          _buildLogoutButton(),

          SizedBox(height: 40.h),
        ],
      ),
    );
  }

  Widget _buildLogoutButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: () {
          LogoutConfirmationDialog.show(context, () => userManager.signOut());
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.transparentColor,
          side: BorderSide(color: AppColors.colorSecondary, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 0,
        ),
        child: Text(
          context.translate(LocalizationKeys.logoutButton),
          style: TextStyle(
            color: AppColors.colorSecondary,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
