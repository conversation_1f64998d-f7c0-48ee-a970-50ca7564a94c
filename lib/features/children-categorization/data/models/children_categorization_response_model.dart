class ChildrenCategoryResponseModel {
  final Guardian guardian;
  final List<Child> children;
  final List<Student> students;
  final List<Student> studentsWithdrawal;
  final List<Student> finalGraduatedStudents;

  ChildrenCategoryResponseModel({
    required this.guardian,
    required this.children,
    required this.students,
    required this.studentsWithdrawal,
    required this.finalGraduatedStudents,
  });

  factory ChildrenCategoryResponseModel.fromJson(Map<String, dynamic> json) {
    return ChildrenCategoryResponseModel(
      guardian: Guardian.from<PERSON><PERSON>(json['guardian']),
      children: (json['children'] as List<dynamic>)
          .map((e) => Child.fromJson(e))
          .toList(),
      students: (json['students'] as List<dynamic>)
          .map((e) => Student.fromJson(e))
          .toList(),
      studentsWithdrawal: (json['studentsWithdrawal'] as List<dynamic>)
          .map((e) => Student.fromJson(e))
          .toList(),
      finalGraduatedStudents: (json['finalGraduatedStudents'] as List<dynamic>)
          .map((e) => Student.fromJson(e))
          .toList(),
    );
  }
}

class Guardian {
  final int id;
  final TranslatedName name;
  final String email;
  final String mobile;
  final TranslatedName surname;
  final TranslatedName nationalityName;
  final int childrenCount;
  final dynamic relativeRelation;
  final String emiratesIdNumber;
  final DateTime birthDate;
  final String gender;
  final int requestCount;

  Guardian({
    required this.id,
    required this.name,
    required this.email,
    required this.mobile,
    required this.surname,
    required this.nationalityName,
    required this.childrenCount,
    required this.relativeRelation,
    required this.emiratesIdNumber,
    required this.birthDate,
    required this.gender,
    required this.requestCount,
  });

  factory Guardian.fromJson(Map<String, dynamic> json) {
    return Guardian(
      id: json['id'],
      name: TranslatedName.fromJson(json['name']),
      email: json['email'],
      mobile: json['mobile'],
      surname: TranslatedName.fromJson(json['surname']),
      nationalityName: TranslatedName.fromJson(json['nationalityName']),
      childrenCount: json['childrenCount'],
      relativeRelation: json['relativeRelation'],
      emiratesIdNumber: json['emiratesIdNumber'],
      birthDate: DateTime.parse(json['birthDate']),
      gender: json['gender'],
      requestCount: json['requestCount'],
    );
  }
}

class Child {
  final int id;
  final TranslatedName name;
  final String gender;
  final int age;
  final String? imagePath;
  final TranslatedName relativeRelation;
  final bool isDelegated;
  final bool hasBeenDelegated;

  Child({
    required this.id,
    required this.name,
    required this.gender,
    required this.age,
    required this.imagePath,
    required this.relativeRelation,
    required this.isDelegated,
    required this.hasBeenDelegated,
  });

  factory Child.fromJson(Map<String, dynamic> json) {
    return Child(
      id: json['id'],
      name: TranslatedName.fromJson(json['name']),
      gender: json['gender'],
      age: json['age'],
      imagePath: json['imagePath'],
      relativeRelation: TranslatedName.fromJson(json['relativeRelation']),
      isDelegated: json['isDelegated'],
      hasBeenDelegated: json['hasBeenDelegated'],
    );
  }
}

class Student {
  final int id;
  final String studentGuid;
  final TranslatedName name;
  final TranslatedName guardian;
  final int age;
  final String? imagePath;
  final TranslatedName relativeRelation;
  final TranslatedName? school;
  final TranslatedName? grade;
  final TranslatedName? curriculum;
  final String? curriculumCode;
  final String? gradeCode;
  final TranslatedName? division;
  final String studentStatus;
  final int? addressEmiratesId;
  final String? emiratesNameEn;
  final String? emiratesNameAr;
  final int? cityId;
  final String? cityNameEn;
  final String? cityNameAr;
  final int? stateId;
  final String? stateNameEn;
  final String? stateNameAr;
  final int? municipalityId;
  final String? municipalityEn;
  final String? municipalityAr;
  final String gender;
  final bool certificateFromSchool;
  final bool rCertificateFromSPEA;
  final int schoolYearId;
  final bool isDelegated;
  final bool hasBeenDelegated;

  Student({
    required this.id,
    required this.studentGuid,
    required this.name,
    required this.guardian,
    required this.age,
    required this.imagePath,
    required this.relativeRelation,
    required this.school,
    required this.grade,
    required this.curriculum,
    required this.curriculumCode,
    required this.gradeCode,
    required this.division,
    required this.studentStatus,
    required this.addressEmiratesId,
    required this.emiratesNameEn,
    required this.emiratesNameAr,
    required this.cityId,
    required this.cityNameEn,
    required this.cityNameAr,
    required this.stateId,
    required this.stateNameEn,
    required this.stateNameAr,
    required this.municipalityId,
    required this.municipalityEn,
    required this.municipalityAr,
    required this.gender,
    required this.certificateFromSchool,
    required this.rCertificateFromSPEA,
    required this.schoolYearId,
    required this.isDelegated,
    required this.hasBeenDelegated,
  });

  factory Student.fromJson(Map<String, dynamic> json) {
    return Student(
      id: json['id'],
      studentGuid: json['studentGuid'],
      name: TranslatedName.fromJson(json['name']),
      guardian: TranslatedName.fromJson(json['guardian']),
      age: json['age'],
      imagePath: json['imagePath'],
      relativeRelation: TranslatedName.fromJson(json['relativeRelation']),
      school: json['school'] != null ? TranslatedName.fromJson(json['school']) : null,
      grade: json['grade'] != null ? TranslatedName.fromJson(json['grade']) : null,
      curriculum: json['curriculum'] != null ? TranslatedName.fromJson(json['curriculum']) : null,
      curriculumCode: json['curriculumCode'],
      gradeCode: json['gradeCode'],
      division: json['division'] != null ? TranslatedName.fromJson(json['division']) : null,
      studentStatus: json['studentStatus'],
      addressEmiratesId: json['addressEmiratesId'],
      emiratesNameEn: json['emiratesNameEn'],
      emiratesNameAr: json['emiratesNameAr'],
      cityId: json['cityId'],
      cityNameEn: json['cityNameEn'],
      cityNameAr: json['cityNameAr'],
      stateId: json['stateId'],
      stateNameEn: json['stateNameEn'],
      stateNameAr: json['stateNameAr'],
      municipalityId: json['municipalityId'],
      municipalityEn: json['municipalityEn'],
      municipalityAr: json['municipalityAr'],
      gender: json['gender'],
      certificateFromSchool: json['certificateFromSchool'],
      rCertificateFromSPEA: json['rCertificateFromSPEA'],
      schoolYearId: json['schoolYearId'],
      isDelegated: json['isDelegated'],
      hasBeenDelegated: json['hasBeenDelegated'],
    );
  }
}

class TranslatedName {
  final String en;
  final String ar;

  TranslatedName({
    required this.en,
    required this.ar,
  });

  factory TranslatedName.fromJson(Map<String, dynamic> json) {
    return TranslatedName(
      en: json['en'] ?? '',
      ar: json['ar'] ?? '',
    );
  }
}