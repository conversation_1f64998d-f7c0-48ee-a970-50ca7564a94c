import 'package:daleel/apis/_base/dio_api_manager.dart';
import '../../../../../apis/api_urls.dart';
import '../../../../../apis/errors/error_api_model.dart';
import '../models/children_categorization_response_model.dart';
import '../models/children_categorization_send_model.dart';

class ChildrenCategorizationApiManager {
  final DioApiManager dioApiManager;

  ChildrenCategorizationApiManager(this.dioApiManager);

  Future<void> getRegisteredChildrenByYear(
      ChildrenCategorizationSendModel sendModel,
      void Function(ChildrenCategoryResponseModel) success,
      void Function(ErrorApiModel) fail,
      ) async {
    await dioApiManager.dio.get(
      ApiUrls.childrenById(sendModel.pathId),
      queryParameters: {
        'schoolYearId': sendModel.schoolYearId,
      },
    ).then((response) {
      final data = response.data as Map<String, dynamic>;
      final model = ChildrenCategoryResponseModel.fromJson(data);
      success(model);
    }).catchError((error) {
      fail(ErrorApiModel.identifyError(error: error));
    });
  }
}
