import '../../domain/repository/base_children_categorization_repository.dart';
import '../datasources/children_categorization_api_manager.dart';
import '../models/children_categorization_response_model.dart';
import '../models/children_categorization_send_model.dart';

class RegisteredChildrenRepository implements BaseChildrenCategorizationRepository {
  final ChildrenCategorizationApiManager registeredChildrenApiManager;

  RegisteredChildrenRepository(this.registeredChildrenApiManager);

  @override
  Future<ChildrenCategoryResponseModel> registerChildren(
      ChildrenCategorizationSendModel registerChildren,
  ) async {
    late ChildrenCategoryResponseModel registeredChildrenResponseModel;
    await registeredChildrenApiManager.getRegisteredChildrenByYear(
      registerChildren,
      (response) {
        registeredChildrenResponseModel = response;
      },
      (errorApiModel) {
        throw errorApiModel;
      },
    );
    return registeredChildrenResponseModel;
  }
}
