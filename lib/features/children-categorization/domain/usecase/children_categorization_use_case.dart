import 'dart:async';
import 'package:daleel/core/usecase.dart';
import 'package:dartz/dartz.dart';
import '../../../../../apis/errors/error_api_model.dart';
import '../../data/models/children_categorization_response_model.dart';
import '../../data/models/children_categorization_send_model.dart';
import '../../data/repository/children_categorization_repository.dart';

class ChildrenCategorizationUseCase
    extends
        BaseUseCase<
          ChildrenCategoryResponseModel,
            ChildrenCategorizationSendModel
        > {
  final RegisteredChildrenRepository registeredChildrenRepository;

  ChildrenCategorizationUseCase(this.registeredChildrenRepository);

  @override
  Future<Either<ErrorApiModel, ChildrenCategoryResponseModel>> call(
      ChildrenCategorizationSendModel params,
  ) async {
    late Either<ErrorApiModel, ChildrenCategoryResponseModel> state;

    await registeredChildrenRepository
        .registerChildren(params)
        .then((response) {
          state = Right(response);
        })
        .catchError((error) {
          final errorApiModel = error as ErrorApiModel;
          state = Left(errorApiModel);
        });

    return state;
  }
}
