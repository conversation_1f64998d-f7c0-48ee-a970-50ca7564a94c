import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import '../../../../../children-details/presentation/children_details_screen.dart';
import '../../../../data/models/children_categorization_response_model.dart';

class ChildrenCard extends StatelessWidget {
  final Student student;

  const ChildrenCard({super.key, required this.student});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 30),
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: InkWell(
          onTap: () {
            final studentID = student.studentGuid;
            context.push(
              ChildrenDetailsScreen.routeName,
              extra: {
                ChildrenDetailsScreen.schoolYearKey:
                student.schoolYearId.toString(),
                ChildrenDetailsScreen.studentIdKey: studentID,
              },
            );
          },
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Card(
                clipBehavior: Clip.none,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 24.0,
                    left: 8.0,
                    right: 8.0,
                    bottom: 8.0,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Text(
                              context.isEnglish
                                  ? student.name.en
                                  : student.name.ar,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: AppColors.colorPrimary,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),

                          Opacity(
                            opacity: 0,
                            child: CircleAvatar(
                              radius: 28,
                              backgroundColor: AppColors.orangeAvatar,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          SvgPicture.asset(
                            AppAssetPaths.icHat,
                            width: 20,
                            height: 20,
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              context.isEnglish
                                  ? student.grade?.en ?? ''
                                  : student.grade?.ar ?? '',
                              style: const TextStyle(
                                fontSize: 11,
                                color: Colors.grey,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          const SizedBox(width: 12),
                          SvgPicture.asset(
                            AppAssetPaths.icBuilder,
                            width: 20,
                            height: 20,
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              context.isEnglish
                                  ? student.school?.en ?? ''
                                  : student.school?.ar ?? '',
                              style: const TextStyle(
                                fontSize: 11,
                                color: Colors.grey,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                top: -20,
                right: 16,
                child: CircleAvatar(
                  radius: 28,
                  backgroundColor: AppColors.orangeAvatar,
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Image.asset(
                      AppAssetPaths.imKidAvatar,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
              Positioned.fill(
                left: 16,
                right: null,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: SvgPicture.asset(
                    AppAssetPaths.icBackArrowChildrenCard,
                    width: 24,
                    height: 24,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
