import 'package:flutter/material.dart';
import '../../../../webview/dynamic_web_view.dart';

class SubmitRegistrationRequestScreen extends StatefulWidget {
  const SubmitRegistrationRequestScreen({super.key});
  static const routeName = "/submit_registration_request";
  @override
  State<SubmitRegistrationRequestScreen> createState() => _SubmitRegistrationRequestScreenState();
}

class _SubmitRegistrationRequestScreenState extends State<SubmitRegistrationRequestScreen> {
  static const String initialUrl = 'https://daleel-spea-front-qa-cpcscfarfndddcbk.eastus-01.azurewebsites.net/parent/child/1036/register-request?status=UnRegistered';
  static const String redirectUrl = '';
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: DynamicWebView(
        url: initialUrl,
        redirectUrl: redirectUrl,
        onSuccessCallback: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('')),
          );
        },
        onFailureCallback: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('')),
          );
        },
      ),
    );
  }
}