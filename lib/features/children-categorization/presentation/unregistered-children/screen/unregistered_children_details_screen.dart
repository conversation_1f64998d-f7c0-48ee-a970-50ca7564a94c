import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import '../../../../../logic/user_manager.dart';
import '../../../../webview/dynamic_web_view.dart';
import '../../../../webview/web_views_urls.dart';

class UnRegisteredChildrenDetailsScreen extends StatefulWidget {
  const UnRegisteredChildrenDetailsScreen({super.key,required this.studentId});

  final String studentId;
  static const children = "children";
  static const routeName = "/unregistered_children_details";
  @override
  State<UnRegisteredChildrenDetailsScreen> createState() => _UnRegisteredChildrenDetailsState();
}

class _UnRegisteredChildrenDetailsState extends State<UnRegisteredChildrenDetailsScreen> {
  final guardianId = GetIt.I<UserMangers>().getGuardianId();

  String unRegisteredChildrenDetailsUrl = '';
  static const String redirectUrl = 'https://daleel-spea-front-qa-cpcscfarfndddcbk.eastus-01.azurewebsites.net/signup-success';

  @override
  void initState() {
    super.initState();
    unRegisteredChildrenDetailsUrl ='${WebViewsUrls.baseWebViewUrl}/parent/$guardianId/child/${widget.studentId}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: DynamicWebView(
        url: unRegisteredChildrenDetailsUrl,
        redirectUrl: redirectUrl,
        onSuccessCallback: () {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('')));
        },
        onFailureCallback: () {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('')));
        },
      ),
    );
  }
}

