import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import '../../data/models/children_categorization_response_model.dart';
import '../../data/models/children_categorization_send_model.dart';
import '../../domain/usecase/children_categorization_use_case.dart';
part 'children_categorization_event.dart';
part 'children_categorization_state.dart';

class ChildrenCategorizationBloc
    extends Bloc<ChildrenCategorizationEvent, RegisteredChildrenState> {
  final ChildrenCategorizationUseCase registeredChildrenUseCase;

  ChildrenCategorizationBloc(this.registeredChildrenUseCase)
    : super(RegisteredChildrenInitial()) {
    on<GetRegisteredChildrenEvent>(_onGetRegisteredChildren);
  }

  Future<void> _onGetRegisteredChildren(
    GetRegisteredChildrenEvent event,
    Emitter<RegisteredChildrenState> emit,
  ) async {
    emit(RegisteredChildrenLoading());

    final result = await registeredChildrenUseCase(event.sendModel);

    result.fold(
      (error) => emit(RegisteredChildrenError(error.message)),
      (response) =>
          emit(RegisteredChildrenLoaded(response.students, response.children, response.studentsWithdrawal, response.finalGraduatedStudents)),
    );
  }
}
