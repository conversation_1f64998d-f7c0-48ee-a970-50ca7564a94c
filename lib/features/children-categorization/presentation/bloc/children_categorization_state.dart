part of 'children_categorization_bloc.dart';

@immutable
sealed class RegisteredChildrenState {}

final class RegisteredChildrenInitial extends RegisteredChildrenState {}

final class RegisteredChildrenLoading extends RegisteredChildrenState {}

final class RegisteredChildrenLoaded extends RegisteredChildrenState {
  final List<Student> students;
  final List<Child> children;
  final List<Student> studentsWithdrawal;
  final List<Student> finalGraduatedStudents;
  RegisteredChildrenLoaded(this.students,this.children,this.studentsWithdrawal,this.finalGraduatedStudents);
}

final class RegisteredChildrenError extends RegisteredChildrenState {
  final String message;

  RegisteredChildrenError(this.message);
}
