import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import '../../../../../apis/_base/dio_api_manager.dart';
import '../../../../../logic/user_manager.dart';
import '../../../../../res/app_asset_paths.dart';
import '../../../../../res/app_colors.dart';
import '../../../../../utils/locale/app_localization_keys.dart';
import '../../../data/datasources/children_categorization_api_manager.dart';
import '../../../data/models/children_categorization_send_model.dart';
import '../../../data/repository/children_categorization_repository.dart';
import '../../../domain/usecase/children_categorization_use_case.dart';
import '../../bloc/children_categorization_bloc.dart';
import '../widget/withdrawn_card.dart';


class WithdrawnChildrenScreen extends StatelessWidget {
  const WithdrawnChildrenScreen({super.key});

  static const routeName = "/withdrawn_children";

  @override
  Widget build(BuildContext context) {
    final dioApiManager = GetIt.I<DioApiManager>();
    final guardianId = GetIt.I<UserMangers>().getGuardianId();
    final schoolYearId = GetIt.I<UserMangers>().getSchoolYearId();
    final registeredChildrenRepository = RegisteredChildrenRepository(
      ChildrenCategorizationApiManager(dioApiManager),
    );

    return BlocProvider<ChildrenCategorizationBloc>(
      create: (_) => ChildrenCategorizationBloc(
        ChildrenCategorizationUseCase(registeredChildrenRepository),
      )..add(
        GetRegisteredChildrenEvent(
          ChildrenCategorizationSendModel(
            guardianId: guardianId!,
            schoolYearId: schoolYearId!,
          ),
        ),
      ),
      child: const WithdrawnChildrenScreenWithBloc(),
    );
  }
}

class WithdrawnChildrenScreenWithBloc extends StatefulWidget {
  const WithdrawnChildrenScreenWithBloc({super.key});

  @override
  State<WithdrawnChildrenScreenWithBloc> createState() =>
      _WithdrawnChildrenScreenWithBlocState();
}

class _WithdrawnChildrenScreenWithBlocState extends State<WithdrawnChildrenScreenWithBloc> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: BlocConsumer<ChildrenCategorizationBloc, RegisteredChildrenState>(
        listener: (context, state) {
          if (state is RegisteredChildrenError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
        },
        builder: (context, state) {
          if (state is RegisteredChildrenLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                _buildHeaderWithTitleOnly(),
                if (state is RegisteredChildrenLoaded) ...[
                  Transform.translate(
                    offset: const Offset(0, -120),
                    child: ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.studentsWithdrawal.length,
                      itemBuilder: (context, index) {
                        final student = state.studentsWithdrawal[index];
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                          child: WithDrawnCard(student: student),
                        );
                      },
                    ),
                  ),
                ] else if (state is RegisteredChildrenError) ...[
                  const SizedBox(height: 12),
                  Text(
                    state.message,
                    style: const TextStyle(color: Colors.red),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeaderWithTitleOnly() {
    return SizedBox(
      height: 270,
      width: double.infinity,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: Image.asset(
              AppAssetPaths.imWithDrawChildren,
              height: 248,
              width: 248,
              fit: BoxFit.cover,
            ),
          ),
          Center(
            child: Column(
              children: [
                const SizedBox(height: 60),
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 60),
                        child: Text(
                          context.translate(LocalizationKeys.sonsWithdrawn),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      left: context.isEnglish ? 0 : null,
                      right: context.isEnglish ? null : 0,
                      child: GestureDetector(
                        onTap: () {
                          context.pop();
                        },
                        child: Container(
                          width: 40,
                          height: 40,
                          margin: const EdgeInsetsDirectional.only(start: 20),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.5),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Icon(
                              Icons.arrow_back_ios_new,
                              color: AppColors.colorHomeButtons,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Divider(
                    thickness: 1,
                    color: AppColors.colorDivider,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}


