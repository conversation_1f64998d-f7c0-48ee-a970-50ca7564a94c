import 'package:flutter/material.dart';
import '../../../../webview/dynamic_web_view.dart';

class WithdrawnReRegistrationScreen extends StatefulWidget {
  const WithdrawnReRegistrationScreen({super.key});

static const routeName = "/withdrawn_re_registration";
  @override
  State<WithdrawnReRegistrationScreen> createState() => _WithdrawnReRegistrationScreenState();
}

class _WithdrawnReRegistrationScreenState extends State<WithdrawnReRegistrationScreen> {
  static const String initialUrl =
      'https://daleel-spea-front-qa-cpcscfarfndddcbk.eastus-01.azurewebsites.net/parent/2/student/0ba831f8-73f6-4bc3-91a3-6e6035dd609b/withdraw-request';
  static const String redirectUrl =
      '';
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: DynamicWebView(
        url: initialUrl,
        redirectUrl: redirectUrl,
        onSuccessCallback: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('')),
          );
        },
        onFailureCallback: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('')),
          );
        },
      ),
    );
  }
}