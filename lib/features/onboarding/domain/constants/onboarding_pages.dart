import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import '../model/onboarding_page_model.dart';

final List<OnboardingPageModel> onboardingPages = [
  OnboardingPageModel(
    image: AppAssetPaths.imgOnboardingPage1,
    title: LocalizationKeys.onboardingTitle1,
    subtitle: LocalizationKeys.onboardingSubtitle1,
  ),
  OnboardingPageModel(
    image: AppAssetPaths.imgOnboardingPage2,
    title: LocalizationKeys.onboardingTitle2,
    subtitle: LocalizationKeys.onboardingSubtitle2,
  ),
  OnboardingPageModel(
    image: AppAssetPaths.imgOnboardingPage3,
    title: LocalizationKeys.onboardingTitle3,
    subtitle: LocalizationKeys.onboardingSubtitle3,
  ),
];
