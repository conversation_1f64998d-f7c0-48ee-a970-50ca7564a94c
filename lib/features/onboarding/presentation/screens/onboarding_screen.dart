import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/login/presentation/screen/login_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../res/app_colors.dart';
import '../../../../utils/locale/app_localization_keys.dart';
import '../../domain/constants/onboarding_pages.dart';
import '../bloc/onboarding_bloc.dart';
import '../widgets/onboarding_page_item.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  static const routeName = "/onboarding";

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with SingleTickerProviderStateMixin {
  final PageController _controller = PageController();

  late AnimationController _bottomController;
  late Animation<Offset> _bottomOffsetAnimation;

  @override
  void initState() {
    super.initState();

    _bottomController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _bottomOffsetAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _bottomController, curve: Curves.easeInOut),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _bottomController.forward();
    });
  }

  void _nextPage() {
    context.read<OnboardingBloc>().add(OnboardingVisibilityChanged(false));

    Future.delayed(const Duration(milliseconds: 200), () {
      context.read<OnboardingBloc>().add(OnboardingNextPressed());
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _bottomController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<OnboardingBloc, OnboardingState>(
        listener: (context, state) {
          if (state is OnboardingUpdatedState) {
            _controller.animateToPage(
              state.currentPage,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeIn,
            );
          } else if (state is OnboardingCompletedState) {
            context.go(LoginScreen.routeName);
          }
        },
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.whiteIcon,
                AppColors.whiteIcon,
                AppColors.blueGradient,
              ],
              stops: [0.5, 0.7, 1.0],
            ),
          ),
          child: Column(
            children: [
              Expanded(
                child: PageView.builder(
                  controller: _controller,
                  itemCount: onboardingPages.length,
                  onPageChanged: (index) {
                    context.read<OnboardingBloc>().add(
                      OnboardingPageChanged(index),
                    );
                  },
                  itemBuilder: (context, index) {
                    return OnboardingPageItem(
                      page: onboardingPages[index],
                      index: index,
                      controller: _controller,
                    );
                  },
                ),
              ),
              SlideTransition(
                position: _bottomOffsetAnimation,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(32.0, 0, 32.0, 40.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      BlocSelector<OnboardingBloc, OnboardingState, int>(
                        selector: (state) => state.currentPage,
                        builder: (context, currentIndex) {
                          return Row(
                            children: List.generate(
                              onboardingPages.length,
                              (i) => AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 2,
                                ),
                                width: currentIndex == i ? 24 : 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: AppColors.colorPrimary,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                      ElevatedButton(
                        onPressed: _nextPage,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.colorPrimary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 6,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Text(
                              context.translate(LocalizationKeys.next),
                              style: const TextStyle(
                                color: AppColors.whiteText,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const SizedBox(width: 10),
                            Container(
                              decoration: const BoxDecoration(
                                color: AppColors.whiteIcon,
                                shape: BoxShape.circle,
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(6),
                                child: Icon(
                                  Icons.arrow_forward_ios,
                                  size: 16,
                                  color: AppColors.colorPrimary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
