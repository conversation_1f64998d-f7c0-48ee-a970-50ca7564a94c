part of 'onboarding_bloc.dart';

@immutable
sealed class OnboardingEvent {}

final class OnboardingPageChanged extends OnboardingEvent {
  final int pageIndex;

  OnboardingPageChanged(this.pageIndex);
}
final class OnboardingNextPressed extends OnboardingEvent {}

final class OnboardingCompleted extends OnboardingEvent {}
final class OnboardingVisibilityChanged extends OnboardingEvent {
  final bool isVisible;

  OnboardingVisibilityChanged(this.isVisible);
}