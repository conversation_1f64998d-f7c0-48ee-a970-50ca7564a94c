part of 'onboarding_bloc.dart';

@immutable
sealed class OnboardingState {
  final int currentPage;
  final bool isVisible;

  const OnboardingState({
    required this.currentPage,
    required this.isVisible,
  });
}

final class OnboardingInitial extends OnboardingState {
  const OnboardingInitial()
      : super(currentPage: 0, isVisible: false);
}

final class OnboardingUpdatedState extends OnboardingState {
  const OnboardingUpdatedState({
    required super.currentPage,
    required super.isVisible,
  });
}

final class OnboardingCompletedState extends OnboardingState {
  const OnboardingCompletedState()
      : super(currentPage: 0, isVisible: false);
}
