import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../res/app_colors.dart';
import '../../domain/model/onboarding_page_model.dart';

class OnboardingPageItem extends StatefulWidget {
  final OnboardingPageModel page;
  final int index;
  final PageController controller;

  const OnboardingPageItem({
    super.key,
    required this.page,
    required this.index,
    required this.controller,
  });

  @override
  State<OnboardingPageItem> createState() => _OnboardingPageItemState();
}

class _OnboardingPageItemState extends State<OnboardingPageItem>
    with TickerProviderStateMixin {
  late AnimationController _imageController;
  late AnimationController _textController;
  late Animation<Offset> _cubeOffsetAnimation;
  late Animation<Offset> _textOffsetAnimation;
  late Animation<Offset> _imageOffsetAnimation;

  bool get isFirstPage => widget.index == 0;

  @override
  void initState() {
    super.initState();

    _imageController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _textController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _cubeOffsetAnimation = Tween<Offset>(
      begin: isFirstPage ? const Offset(-1.0, 0.0) : Offset.zero,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _imageController, curve: Curves.easeInOut),
    );

    _imageOffsetAnimation = Tween<Offset>(
      begin: isFirstPage ? const Offset(-1.0, 0.0) : Offset.zero,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _imageController, curve: Curves.easeInOut),
    );

    _textOffsetAnimation = Tween<Offset>(
      begin: isFirstPage ? const Offset(1.0, 0.0) : Offset.zero,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeInOut),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (isFirstPage) {
        await Future.delayed(const Duration(milliseconds: 100));
        _imageController.forward();
        await Future.delayed(const Duration(milliseconds: 100));
        _textController.forward();
      } else {
        _imageController.value = 1.0;
        _textController.value = 1.0;
      }
    });
  }

  @override
  void dispose() {
    _imageController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final page = widget.page;
    final screenHeight = MediaQuery.of(context).size.height;

    return Stack(
      children: [
        Column(
          children: [
            Expanded(
              flex: 3,
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(50),
                  bottomRight: Radius.circular(50),
                ),
                child: SlideTransition(
                  position: _imageOffsetAnimation,
                  child: Image.asset(
                    page.image,
                    fit: BoxFit.cover,
                    width: double.infinity,
                  ),
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsetsDirectional.only(
                  top: 22,
                  start: 32,
                  end: 82,
                ),
                child: SlideTransition(
                  position: _textOffsetAnimation,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.translate(page.title),
                        style: const TextStyle(
                          fontSize: 24,
                          color: AppColors.colorPrimary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Padding(
                        padding: const EdgeInsetsDirectional.only(end: 32),
                        child: Text(
                          context.translate(page.subtitle),
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.subTitleGray,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        Positioned(
          top: screenHeight * 0.45,
          left: 0,
          child: SlideTransition(
            position: _cubeOffsetAnimation,
            child: SvgPicture.asset(AppAssetPaths.icCubeOnboarding, width: 90),
          ),
        ),
        if (!isFirstPage)
          Positioned(
            top: 40,
            right: 16,
            child: IconButton(
              icon: SvgPicture.asset(AppAssetPaths.icBackArrow),
              onPressed: () {
                if (widget.index > 0) {
                  widget.controller.animateToPage(
                    widget.index - 1,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                }
              },
            ),
          ),
      ],
    );
  }
}
