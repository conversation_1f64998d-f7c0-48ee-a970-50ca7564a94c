import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/add_son/presentation/screen/add_son_without_id/bloc/add_son_withoutId_bloc_extension.dart';
import 'package:daleel/features/add_son/presentation/screen/add_son_without_id/bloc/add_son_without_id_bloc.dart';
import 'package:daleel/features/webview/dynamic_web_view.dart';
import 'package:daleel/features/webview/web_views_urls.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class AddSonWithoutIdScreen extends StatelessWidget {
  const AddSonWithoutIdScreen({super.key});

  static const routeName = "/add-son-without-id";

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AddSonWithoutIdBloc>(
      create: (context) => AddSonWithoutIdBloc(),
      child: const AddSonWithoutIdWithBloc(),
    );
  }
}

class AddSonWithoutIdWithBloc extends StatelessWidget {
  const AddSonWithoutIdWithBloc({super.key});

  @override
  Widget build(BuildContext context) {
    final String webViewUrl = WebViewsUrls.baseWebViewUrl + WebViewsUrls.addChildWithoutID;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.translate(LocalizationKeys.addNewSonTitle),
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.w700),
        ),
        backgroundColor: AppColors.whiteIcon,
        foregroundColor: AppColors.buttonBlackTextColor,
        centerTitle: true,
        leading: IconButton(
          color: AppColors.buttonBlackTextColor,
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: BlocBuilder<AddSonWithoutIdBloc, AddSonWithoutIdState>(
        builder: (context, state) {
          return Stack(
            children: [
              DynamicWebView(
                url: webViewUrl,
                redirectUrl: webViewUrl,
                onLoadStart:
                    () => context.addAddSonWithoutIdEvent(
                      const WebViewLoadStartEvent(),
                    ),
                onLoadStop:
                    () => context.addAddSonWithoutIdEvent(
                      const WebViewLoadStopEvent(),
                    ),
                onSuccessCallback:
                    () => context.addAddSonWithoutIdEvent(
                      const WebViewSuccessEvent(),
                    ),
                onFailureCallback:
                    () => context.addAddSonWithoutIdEvent(
                      const WebViewFailureEvent(),
                    ),
              ),
              if (state is WebViewLoadingState)
                Container(
                  color: AppColors.scaffoldBackground,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(color: AppColors.colorSecondary),
                        const SizedBox(height: 16),
                        Text(
                          context.translate(LocalizationKeys.plzWait),
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.colorSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}