import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'AddSonWithoutIdEvent.dart';
part 'AddSonWithoutIdState.dart';

class AddSonWithoutIdBloc extends Bloc<AddSonWithoutIdEvent, AddSonWithoutIdState> {
  AddSonWithoutIdBloc() : super(const WebViewLoadingState()) {
    on<WebViewLoadStartEvent>(_onWebViewLoadStart);
    on<WebViewLoadStopEvent>(_onWebViewLoadStop);
    on<WebViewSuccessEvent>(_onWebViewSuccess);
    on<WebViewFailureEvent>(_onWebViewFailure);
  }

  void _onWebViewLoadStart(
    WebViewLoadStartEvent event,
    Emitter<AddSonWithoutIdState> emit,
  ) {
    emit(const WebViewLoadingState());
  }

  void _onWebViewLoadStop(
    WebViewLoadStopEvent event,
    Emitter<AddSonWithoutIdState> emit,
  ) {
    emit(const WebViewLoadedState());
  }

  void _onWebViewSuccess(
    WebViewSuccessEvent event,
    Emitter<AddSonWithoutIdState> emit,
  ) {
    emit(const WebViewLoadedState());
  }

  void _onWebViewFailure(
    WebViewFailureEvent event,
    Emitter<AddSonWithoutIdState> emit,
  ) {
    emit(const WebViewLoadedState());
  }
}
