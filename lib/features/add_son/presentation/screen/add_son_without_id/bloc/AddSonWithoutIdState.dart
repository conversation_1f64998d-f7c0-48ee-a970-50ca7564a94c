part of 'add_son_without_id_bloc.dart';

abstract class AddSonWithoutIdState extends Equatable {
  const AddSonWithoutIdState();

  @override
  List<Object?> get props => [];
}

class AddSonWithoutIdInitialState extends AddSonWithoutIdState {
  const AddSonWithoutIdInitialState();
}

class WebViewLoadingState extends AddSonWithoutIdState {
  const WebViewLoadingState();
}

class WebViewLoadedState extends AddSonWithoutIdState {
  const WebViewLoadedState();
}
