part of 'add_son_without_id_bloc.dart';

abstract class AddSonWithoutIdEvent extends Equatable {
  const AddSonWithoutIdEvent();

  @override
  List<Object?> get props => [];
}

class WebViewLoadStartEvent extends AddSonWithoutIdEvent {
  const WebViewLoadStartEvent();
}

class WebViewLoadStopEvent extends AddSonWithoutIdEvent {
  const WebViewLoadStopEvent();
}

class WebViewSuccessEvent extends AddSonWithoutIdEvent {
  const WebViewSuccessEvent();
}

class WebViewFailureEvent extends AddSonWithoutIdEvent {
  const WebViewFailureEvent();
}
