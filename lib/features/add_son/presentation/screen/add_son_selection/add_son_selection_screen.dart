import 'package:daleel/features/add_son/presentation/screen/add_son_with_id/screen/add_son_with_id_screen.dart';
import 'package:daleel/features/add_son/presentation/screen/add_son_without_id/screen/add_son_without_id_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../../../../../core/extensions/extension_localization.dart';
import '../../../../../utils/locale/app_localization_keys.dart';
import '../../../../../res/app_colors.dart';

class AddSonSelectionScreen extends StatelessWidget {
  const AddSonSelectionScreen({super.key});

  static const routeName = "/add-son-selection";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: Text(
          context.translate(LocalizationKeys.addNewSonTitle),
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.w700),
        ),
        backgroundColor: AppColors.whiteIcon,
        foregroundColor: AppColors.buttonBlackTextColor,
        centerTitle: true,
        leading: IconButton(
          color: AppColors.buttonBlackTextColor,
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          children: [
            SizedBox(height: 40.h),

            // Son with ID option
            _buildOptionCard(
              context: context,
              title: context.translate(LocalizationKeys.sonwithId),
              description: context.translate(
                LocalizationKeys.sonWithIdDescription,
              ),
              onTap: () {
                context.push(AddSonWithIdScreen.routeName);
              },
            ),

            SizedBox(height: 20.h),

            // Son without ID option
            _buildOptionCard(
              context: context,
              title: context.translate(LocalizationKeys.sonWithoutId),
              description: context.translate(
                LocalizationKeys.sonWithoutIdDescription,
              ),
              onTap: () {
                context.push(AddSonWithoutIdScreen.routeName);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionCard({
    required BuildContext context,
    required String? title,
    required String? description,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.whiteText,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: AppColors.buttonBlackTextColor.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: AppColors.colorPrimary,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    description ?? '',
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w400,
                      color: AppColors.subTitleGray,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.colorPrimary,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
