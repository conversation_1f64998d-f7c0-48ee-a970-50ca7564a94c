import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/add_son/presentation/screen/add_son_with_id/bloc/add_son_with_id_bloc.dart';
import 'package:daleel/features/add_son/presentation/screen/add_son_with_id/bloc/add_son_withid_bloc_extension.dart';
import 'package:daleel/features/webview/dynamic_web_view.dart';
import 'package:daleel/features/webview/web_views_urls.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class AddSonWithIdScreen extends StatelessWidget {
  const AddSonWithIdScreen({super.key});
  
  static const routeName = "/add-son-with-id";

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AddSonWithIdBloc>(
      create: (context) => AddSonWithIdBloc(),
      child: const AddSonWithIdWithBloc(),
    );
  }
}

class AddSonWithIdWithBloc extends StatelessWidget {
  const AddSonWithIdWithBloc({super.key});

  @override
  Widget build(BuildContext context) {
    final String webViewUrl = WebViewsUrls.baseWebViewUrl + WebViewsUrls.addChildWithID;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.translate(LocalizationKeys.addNewSonTitle),
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.w700),
        ),
        backgroundColor: AppColors.whiteIcon,
        foregroundColor: AppColors.buttonBlackTextColor,
        centerTitle: true,
        leading: IconButton(
          color: AppColors.buttonBlackTextColor,
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: BlocBuilder<AddSonWithIdBloc, AddSonWithIdState>(
        builder: (context, state) {
          return Stack(
            children: [
              DynamicWebView(
                url: webViewUrl,
                redirectUrl: webViewUrl,
                onLoadStart:
                    () => context.addAddSonWithIdEvent(
                      const WebViewLoadStartEvent(),
                    ),
                onLoadStop:
                    () => context.addAddSonWithIdEvent(
                      const WebViewLoadStopEvent(),
                    ),
                onSuccessCallback:
                    () => context.addAddSonWithIdEvent(
                      const WebViewSuccessEvent(),
                    ),
                onFailureCallback:
                    () => context.addAddSonWithIdEvent(
                      const WebViewFailureEvent(),
                    ),
              ),
              if (state is WebViewLoadingState)
                Container(
                  color: AppColors.scaffoldBackground,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(color: AppColors.colorSecondary),
                        const SizedBox(height: 16),
                        Text(
                          context.translate(LocalizationKeys.plzWait),
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.colorSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
