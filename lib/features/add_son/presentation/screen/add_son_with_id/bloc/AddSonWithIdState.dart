part of 'add_son_with_id_bloc.dart';

abstract class AddSonWithIdState extends Equatable {
  const AddSonWithIdState();

  @override
  List<Object?> get props => [];
}

class AddSonWithIdInitialState extends AddSonWithIdState {
  const AddSonWithIdInitialState();
}

class WebViewLoadingState extends AddSonWithIdState {
  const WebViewLoadingState();
}

class WebViewLoadedState extends AddSonWithIdState {
  const WebViewLoadedState();
}
