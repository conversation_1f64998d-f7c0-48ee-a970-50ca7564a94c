import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'AddSonWithIdEvent.dart';
part 'AddSonWithIdState.dart';

class AddSonWithIdBloc extends Bloc<AddSonWithIdEvent, AddSonWithIdState> {
  AddSonWithIdBloc() : super(const WebViewLoadingState()) {
    on<WebViewLoadStartEvent>(_onWebViewLoadStart);
    on<WebViewLoadStopEvent>(_onWebViewLoadStop);
    on<WebViewSuccessEvent>(_onWebViewSuccess);
    on<WebViewFailureEvent>(_onWebViewFailure);
  }

  void _onWebViewLoadStart(
    WebViewLoadStartEvent event,
    Emitter<AddSonWithIdState> emit,
  ) {
    emit(const WebViewLoadingState());
  }

  void _onWebViewLoadStop(
    WebViewLoadStopEvent event,
    Emitter<AddSonWithIdState> emit,
  ) {
    emit(const WebViewLoadedState());
  }

  void _onWebViewSuccess(
    WebViewSuccessEvent event,
    Emitter<AddSonWithIdState> emit,
  ) {
    emit(const WebViewLoadedState());
  }

  void _onWebViewFailure(
    WebViewFailureEvent event,
    Emitter<AddSonWithIdState> emit,
  ) {
    emit(const WebViewLoadedState());
  }
}
