part of 'add_son_with_id_bloc.dart';

abstract class AddSonWithIdEvent extends Equatable {
  const AddSonWithIdEvent();

  @override
  List<Object?> get props => [];
}

class WebViewLoadStartEvent extends AddSonWithIdEvent {
  const WebViewLoadStartEvent();
}

class WebViewLoadStopEvent extends AddSonWithIdEvent {
  const WebViewLoadStopEvent();
}

class WebViewSuccessEvent extends AddSonWithIdEvent {
  const WebViewSuccessEvent();
}

class WebViewFailureEvent extends AddSonWithIdEvent {
  const WebViewFailureEvent();
}
