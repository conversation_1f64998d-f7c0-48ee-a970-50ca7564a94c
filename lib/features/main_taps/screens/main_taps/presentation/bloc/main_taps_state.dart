part of 'main_taps_bloc.dart';

abstract class MainTapsState extends Equatable {
  const MainTapsState();

  @override
  List<Object> get props => [];
}

class MainTapsInitialState extends MainTapsState {}

class MainTapsLoadingState extends MainTapsState {}

class MainTapsLoadedState extends MainTapsState {
  final int currentIndex;
  final List<Widget> screens;

  const MainTapsLoadedState({
    required this.currentIndex,
    required this.screens,
  });

  @override
  List<Object> get props => [currentIndex, screens];
}

class MainTapsTabChangedState extends MainTapsState {
  final int currentIndex;

  const MainTapsTabChangedState(this.currentIndex);

  @override
  List<Object> get props => [currentIndex];
}

class MainTapsErrorState extends MainTapsState {
  final String errorMessage;
  final bool isLocalizationKey;

  const MainTapsErrorState(this.errorMessage, this.isLocalizationKey);

  @override
  List<Object> get props => [errorMessage, isLocalizationKey];
}