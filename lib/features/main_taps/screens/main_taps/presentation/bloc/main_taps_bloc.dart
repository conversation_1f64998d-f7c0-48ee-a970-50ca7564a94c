import 'package:daleel/features/main_taps/screens/certificates/presentation/screen/certificates_screen.dart';
import 'package:daleel/features/main_taps/screens/chats/presentation/screens/chats_screen.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/screen/home_screen.dart';
import 'package:daleel/features/main_taps/screens/requests/presentation/screens/requests_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'main_taps_event.dart';
part 'main_taps_state.dart';

class MainTapsBloc extends Bloc<MainTapsEvent, MainTapsState> {
  int _currentIndex = 0;
  late List<Widget> _screens;

  MainTapsBloc() : super(MainTapsInitialState()) {
    on<InitMainTapsEvent>(_onInitMainTaps);
    on<ChangeTabEvent>(_onChangeTab);
  }

  int get currentIndex => _currentIndex;
  List<Widget> get screens => _screens;

  Future<void> _onInitMainTaps(InitMainTapsEvent event, Emitter<MainTapsState> emit) async {
    emit(MainTapsLoadingState());
    try {
      _screens = [
        HomeScreen(),
        const CertificatesScreen(),
        const RequestsScreen(),
        const ChatsScreen(),
      ];
      
      emit(MainTapsLoadedState(
        currentIndex: _currentIndex,
        screens: _screens,
      ));
    } catch (e) {
      emit(MainTapsErrorState(e.toString(), false));
    }
  }

  Future<void> _onChangeTab(ChangeTabEvent event, Emitter<MainTapsState> emit) async {
    try {
      _currentIndex = event.index;
      emit(MainTapsTabChangedState(_currentIndex));
    } catch (e) {
      emit(MainTapsErrorState(e.toString(), false));
    }
  }
}