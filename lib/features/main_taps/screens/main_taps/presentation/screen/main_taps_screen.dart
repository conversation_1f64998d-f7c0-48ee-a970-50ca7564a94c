import 'package:daleel/core/widgets/base_stateful_screen_widget.dart';
import 'package:daleel/features/main_taps/screens/main_taps/presentation/bloc/main_taps_bloc.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/utils/feedback/feedback_message.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MainTapsScreen extends StatelessWidget {
  const MainTapsScreen({super.key});

  static const String routeName = '/maintabs';

  @override
  Widget build(BuildContext context) {
    return BlocProvider<MainTapsBloc>(
      create: (context) => MainTapsBloc()..add(InitMainTapsEvent()),
      child: const MainTapsScreenWithBloc(),
    );
  }
}

class MainTapsScreenWithBloc extends BaseStatefulScreenWidget {
  const MainTapsScreenWithBloc({super.key});

  @override
  BaseScreenState<MainTapsScreenWithBloc> baseScreenCreateState() =>
      _MainTapsScreenWithBlocState();
}

class _MainTapsScreenWithBlocState
    extends BaseScreenState<MainTapsScreenWithBloc> {
  @override
  Widget baseScreenBuild(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<MainTapsBloc, MainTapsState>(
        listener: (context, state) {
          if (state is MainTapsLoadingState) {
            showLoading();
          } else {
            hideLoading();
          }
          if (state is MainTapsErrorState) {
            showFeedbackMessage(state.errorMessage);
          }
        },
        builder: (context, state) => _buildMainTapsWidget(state),
      ),
    );
  }

  Widget _buildMainTapsWidget(MainTapsState state) {
    if (state is MainTapsLoadedState || state is MainTapsTabChangedState) {
      final currentIndex =
          state is MainTapsLoadedState
              ? state.currentIndex
              : (state as MainTapsTabChangedState).currentIndex;

      final screens =
          state is MainTapsLoadedState ? state.screens : currentBloc.screens;

      return Scaffold(
        bottomNavigationBar: _buildBottomNavigationBar(currentIndex),
        body: IndexedStack(index: currentIndex, children: screens),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildBottomNavigationBar(int currentIndex) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentIndex,
      onTap: (index) {
        currentBloc.add(ChangeTabEvent(index));
      },
      items: [
        BottomNavigationBarItem(
          icon: Image.asset(
            currentIndex == 0
                ? AppAssetPaths.ic_selected_home
                : AppAssetPaths.ic_home,
            width: 30,
            height: 30,
          ),
          label: context.translate(LocalizationKeys.homeTab),
        ),
        BottomNavigationBarItem(
          icon: Image.asset(
            currentIndex == 1
                ? AppAssetPaths.ic_selected_certificates
                : AppAssetPaths.ic_card,
            width: 30,
            height: 30,
          ),
          label: context.translate(LocalizationKeys.certificatesTab),
        ),
        BottomNavigationBarItem(
          icon: Image.asset(
            currentIndex == 2
                ? AppAssetPaths.ic_selected_requests
                : AppAssetPaths.ic_article,
            width: 30,
            height: 30,
          ),
          label: context.translate(LocalizationKeys.requestsTab),
        ),
        BottomNavigationBarItem(
          icon: Image.asset(
            currentIndex == 3
                ? AppAssetPaths.ic_selected_chat
                : AppAssetPaths.ic_chat,
            width: 30,
            height: 30,
          ),
          label: context.translate(LocalizationKeys.chatsTab),
        ),
      ],
    );
  }

  MainTapsBloc get currentBloc => context.read<MainTapsBloc>();
}
