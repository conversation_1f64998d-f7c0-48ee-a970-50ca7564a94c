import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/main_taps/screens/requests/presentation/bloc/requests_bloc.dart';
import 'package:daleel/features/main_taps/screens/requests/presentation/bloc/requests_bloc_extension.dart';
import 'package:daleel/features/webview/dynamic_web_view.dart';
import 'package:daleel/features/webview/web_views_urls.dart';
import 'package:daleel/features/widgets/page_header/page_header.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RequestsScreen extends StatelessWidget {
  const RequestsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<RequestsBloc>(
      create: (context) => RequestsBloc(),
      child: const RequestsScreenWithBloc(),
    );
  }
}

class RequestsScreenWithBloc extends StatelessWidget {
  const RequestsScreenWithBloc({super.key});

  @override
  Widget build(BuildContext context) {
    final String webViewUrl = WebViewsUrls.baseWebViewUrl + WebViewsUrls.requestsList;

    return Scaffold(
      appBar: PageHeader(title: context.translate(LocalizationKeys.requests)),
      body: BlocBuilder<RequestsBloc, RequestsState>(
        builder: (context, state) {
          return Stack(
            children: [
              DynamicWebView(
                url: webViewUrl,
                redirectUrl: webViewUrl,
                onLoadStart:
                    () => context.addRequestsEvent(
                      const WebViewLoadStartEvent(),
                    ),
                onLoadStop:
                    () => context.addRequestsEvent(
                      const WebViewLoadStopEvent(),
                    ),
                onSuccessCallback:
                    () => context.addRequestsEvent(
                      const WebViewSuccessEvent(),
                    ),
                onFailureCallback:
                    () => context.addRequestsEvent(
                      const WebViewFailureEvent(),
                    ),
              ),
              if (state is WebViewLoadingState)
                Container(
                  color: AppColors.scaffoldBackground,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(color: AppColors.colorSecondary),
                        const SizedBox(height: 16),
                        Text(
                          context.translate(LocalizationKeys.plzWait),
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.colorSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
