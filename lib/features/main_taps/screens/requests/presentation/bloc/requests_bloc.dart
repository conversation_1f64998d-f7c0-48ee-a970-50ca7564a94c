import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'requests_event.dart';
part 'requests_state.dart';

class RequestsBloc extends Bloc<RequestsEvent, RequestsState> {
  RequestsBloc() : super(const WebViewLoadingState()) {
    on<WebViewLoadStartEvent>(_onWebViewLoadStart);
    on<WebViewLoadStopEvent>(_onWebViewLoadStop);
    on<WebViewSuccessEvent>(_onWebViewSuccess);
    on<WebViewFailureEvent>(_onWebViewFailure);
  }

  void _onWebViewLoadStart(
    WebViewLoadStartEvent event,
    Emitter<RequestsState> emit,
  ) {
    emit(const WebViewLoadingState());
  }

  void _onWebViewLoadStop(
    WebViewLoadStopEvent event,
    Emitter<RequestsState> emit,
  ) {
    emit(const WebViewLoadedState());
  }

  void _onWebViewSuccess(
    WebViewSuccessEvent event,
    Emitter<RequestsState> emit,
  ) {
    emit(const WebViewLoadedState());
  }

  void _onWebViewFailure(
    WebViewFailureEvent event,
    Emitter<RequestsState> emit,
  ) {
    emit(const WebViewLoadedState());
  }
}
