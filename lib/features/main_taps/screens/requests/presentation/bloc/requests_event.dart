part of 'requests_bloc.dart';

abstract class RequestsEvent extends Equatable {
  const RequestsEvent();

  @override
  List<Object?> get props => [];
}

class WebViewLoadStartEvent extends RequestsEvent {
  const WebViewLoadStartEvent();
}

class WebViewLoadStopEvent extends RequestsEvent {
  const WebViewLoadStopEvent();
}

class WebViewSuccessEvent extends RequestsEvent {
  const WebViewSuccessEvent();
}

class WebViewFailureEvent extends RequestsEvent {
  const WebViewFailureEvent();
}
