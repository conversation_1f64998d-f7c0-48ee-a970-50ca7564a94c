part of 'requests_bloc.dart';

abstract class RequestsState extends Equatable {
  const RequestsState();

  @override
  List<Object?> get props => [];
}

class RequestsInitialState extends RequestsState {
  const RequestsInitialState();
}

class WebViewLoadingState extends RequestsState {
  const WebViewLoadingState();
}

class WebViewLoadedState extends RequestsState {
  const WebViewLoadedState();
}
