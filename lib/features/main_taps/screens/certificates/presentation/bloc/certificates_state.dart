part of 'certificates_bloc.dart';

abstract class CertificatesState extends Equatable {
  const CertificatesState();

  @override
  List<Object?> get props => [];
}

class CertificatesInitialState extends CertificatesState {
  const CertificatesInitialState();
}

class WebViewLoadingState extends CertificatesState {
  const WebViewLoadingState();
}

class WebViewLoadedState extends CertificatesState {
  const WebViewLoadedState();
}
