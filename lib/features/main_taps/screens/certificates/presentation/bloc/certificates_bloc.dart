import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'certificates_event.dart';
part 'certificates_state.dart';

class CertificatesBloc extends Bloc<CertificatesEvent, CertificatesState> {
  CertificatesBloc() : super(const WebViewLoadingState()) {
    on<WebViewLoadStartEvent>(_onWebViewLoadStart);
    on<WebViewLoadStopEvent>(_onWebViewLoadStop);
    on<WebViewSuccessEvent>(_onWebViewSuccess);
    on<WebViewFailureEvent>(_onWebViewFailure);
  }

  void _onWebViewLoadStart(
    WebViewLoadStartEvent event,
    Emitter<CertificatesState> emit,
  ) {
    emit(const WebViewLoadingState());
  }

  void _onWebViewLoadStop(
    WebViewLoadStopEvent event,
    Emitter<CertificatesState> emit,
  ) {
    emit(const WebViewLoadedState());
  }

  void _onWebViewSuccess(
    WebViewSuccessEvent event,
    Emitter<CertificatesState> emit,
  ) {
    emit(const WebViewLoadedState());
  }

  void _onWebViewFailure(
    WebViewFailureEvent event,
    Emitter<CertificatesState> emit,
  ) {
    emit(const WebViewLoadedState());
  }
}
