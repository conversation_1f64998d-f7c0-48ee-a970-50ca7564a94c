part of 'certificates_bloc.dart';

abstract class CertificatesEvent extends Equatable {
  const CertificatesEvent();

  @override
  List<Object?> get props => [];
}

class WebViewLoadStartEvent extends CertificatesEvent {
  const WebViewLoadStartEvent();
}

class WebViewLoadStopEvent extends CertificatesEvent {
  const WebViewLoadStopEvent();
}

class WebViewSuccessEvent extends CertificatesEvent {
  const WebViewSuccessEvent();
}

class WebViewFailureEvent extends CertificatesEvent {
  const WebViewFailureEvent();
}
