import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/main_taps/screens/chats/presentation/bloc/chats_bloc.dart';
import 'package:daleel/features/main_taps/screens/chats/presentation/bloc/chats_bloc_extension.dart';
import 'package:daleel/features/webview/dynamic_web_view.dart';
import 'package:daleel/features/webview/web_views_urls.dart';
import 'package:daleel/features/widgets/page_header/page_header.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChatsScreen extends StatelessWidget {
  const ChatsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ChatsBloc>(
      create: (context) => ChatsBloc(),
      child: const ChatsScreenWithBloc(),
    );
  }
}

class ChatsScreenWithBloc extends StatelessWidget {
  const ChatsScreenWithBloc({super.key});

  @override
  Widget build(BuildContext context) {
    final String webViewUrl = WebViewsUrls.baseWebViewUrl + WebViewsUrls.messages;

    return Scaffold(
      appBar: PageHeader(title: context.translate(LocalizationKeys.chatsTab)),
      body: BlocBuilder<ChatsBloc, ChatsState>(
        builder: (context, state) {
          return Stack(
            children: [
              DynamicWebView(
                url: webViewUrl,
                redirectUrl: webViewUrl,
                onLoadStart:
                    () => context.addChatsEvent(
                      const WebViewLoadStartEvent(),
                    ),
                onLoadStop:
                    () => context.addChatsEvent(
                      const WebViewLoadStopEvent(),
                    ),
                onSuccessCallback:
                    () => context.addChatsEvent(
                      const WebViewSuccessEvent(),
                    ),
                onFailureCallback:
                    () => context.addChatsEvent(
                      const WebViewFailureEvent(),
                    ),
              ),
              if (state is WebViewLoadingState)
                Container(
                  color: AppColors.scaffoldBackground,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(color: AppColors.colorSecondary),
                        const SizedBox(height: 16),
                        Text(
                          context.translate(LocalizationKeys.plzWait),
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.colorSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
