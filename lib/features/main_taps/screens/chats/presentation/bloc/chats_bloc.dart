import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'chats_event.dart';
part 'chats_state.dart';

class ChatsBloc extends Bloc<ChatsEvent, ChatsState> {
  ChatsBloc() : super(const WebViewLoadingState()) {
    on<WebViewLoadStartEvent>(_onWebViewLoadStart);
    on<WebViewLoadStopEvent>(_onWebViewLoadStop);
    on<WebViewSuccessEvent>(_onWebViewSuccess);
    on<WebViewFailureEvent>(_onWebViewFailure);
  }

  void _onWebViewLoadStart(
    WebViewLoadStartEvent event,
    Emitter<ChatsState> emit,
  ) {
    emit(const WebViewLoadingState());
  }

  void _onWebViewLoadStop(
    WebViewLoadStopEvent event,
    Emitter<ChatsState> emit,
  ) {
    emit(const WebViewLoadedState());
  }

  void _onWebViewSuccess(
    WebViewSuccessEvent event,
    Emitter<ChatsState> emit,
  ) {
    emit(const WebViewLoadedState());
  }

  void _onWebViewFailure(
    WebViewFailureEvent event,
    Emitter<ChatsState> emit,
  ) {
    emit(const WebViewLoadedState());
  }
}
