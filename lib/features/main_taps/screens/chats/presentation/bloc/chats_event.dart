part of 'chats_bloc.dart';

abstract class ChatsEvent extends Equatable {
  const ChatsEvent();

  @override
  List<Object?> get props => [];
}

class WebViewLoadStartEvent extends ChatsEvent {
  const WebViewLoadStartEvent();
}

class WebViewLoadStopEvent extends ChatsEvent {
  const WebViewLoadStopEvent();
}

class WebViewSuccessEvent extends ChatsEvent {
  const WebViewSuccessEvent();
}

class WebViewFailureEvent extends ChatsEvent {
  const WebViewFailureEvent();
}
