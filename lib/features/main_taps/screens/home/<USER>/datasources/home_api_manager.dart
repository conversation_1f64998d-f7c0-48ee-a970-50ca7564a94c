import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/apis/api_keys.dart';
import 'package:daleel/apis/api_urls.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/guardian_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/current_school_year_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_enums_value_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/certificate_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';

class HomeApiManager {
  final DioApiManager dioApiManager;

  HomeApiManager(this.dioApiManager);

  Future<void> getCurrentSchoolYearApi(
    void Function(CurrentSchoolYearApiModel) success,
    void Function(ErrorApiModel) fail,
  ) async {
    await dioApiManager.dio
        .get(ApiUrls.currentSchoolYear)
        .then((response) {
          Map<String, dynamic> extractedData =
              response.data as Map<String, dynamic>;
          CurrentSchoolYearApiModel schoolYear =
              CurrentSchoolYearApiModel.fromJson(extractedData);
          success(schoolYear);
        })
        .catchError((error) {
          fail(ErrorApiModel.identifyError(error: error));
        });
  }

  Future<void> getRequestEnumValuesApi(
    void Function(RequestEnumValuesApiModel) success,
    void Function(ErrorApiModel) fail,
  ) async {
    await dioApiManager.dio
        .get(ApiUrls.requestEnumValues)
        .then((response) {
          Map<String, dynamic> extractedData =
              response.data as Map<String, dynamic>;
          RequestEnumValuesApiModel enumValues =
              RequestEnumValuesApiModel.fromJson(extractedData);
          success(enumValues);
        })
        .catchError((error) {
          fail(ErrorApiModel.identifyError(error: error));
        });
  }

  Future<void> getCertificatesApi(
    int schoolYearId,
    void Function(CertificateResponse) success,
    void Function(ErrorApiModel) fail,
  ) async {
    final requestBody = {
      "page": ApiKeys.zeroPageValue,
      "pageSize": ApiKeys.certificatesPageValue,
    };
    await dioApiManager.dio
        .post(
          ApiUrls.certificates,
          queryParameters: {"schoolYearId": schoolYearId},
          data: requestBody,
        )
        .then((response) {
          Map<String, dynamic> extractedData =
              response.data as Map<String, dynamic>;
          CertificateResponse certificates = CertificateResponse.fromJson(
            extractedData,
          );
          success(certificates);
        })
        .catchError((error) {
          fail(ErrorApiModel.identifyError(error: error));
        });
  }

  Future<void> getMyRequestsApi(
    Map<String, dynamic> requestBody,
    void Function(RequestResponse) success,
    void Function(ErrorApiModel) fail,
  ) async {
    await dioApiManager.dio
        .post(ApiUrls.myRequests, data: requestBody)
        .then((response) {
          Map<String, dynamic> extractedData =
              response.data as Map<String, dynamic>;
          RequestResponse requests = RequestResponse.fromJson(extractedData);
          success(requests);
        })
        .catchError((error) {
          fail(ErrorApiModel.identifyError(error: error));
        });
  }

  Future<void> getGuardianByUserIdApi(
    void Function(GuardianApiModel) success,
    void Function(ErrorApiModel) fail,
  ) async {
    await dioApiManager.dio
        .get(ApiUrls.guardianByUserId)
        .then((response) {
          Map<String, dynamic> extractedData =
              response.data as Map<String, dynamic>;
          GuardianApiModel guardian = GuardianApiModel.fromJson(extractedData);
          success(guardian);
        })
        .catchError((error) {
          fail(ErrorApiModel.identifyError(error: error));
        });
  }
}
