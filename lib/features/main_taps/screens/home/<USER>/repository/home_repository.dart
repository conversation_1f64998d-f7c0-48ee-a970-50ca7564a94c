import 'package:daleel/features/main_taps/screens/home/<USER>/datasources/home_api_manager.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/current_school_year_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_enums_value_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/certificate_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/guardian_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repositories/base_home_repository.dart';

class HomeRepository implements BaseHomeRepository {
  final HomeApiManager homeApiManager;

  HomeRepository(this.homeApiManager);

  @override
  Future<CurrentSchoolYearApiModel> getCurrentSchoolYear() async {
    late CurrentSchoolYearApiModel response;
    await homeApiManager.getCurrentSchoolYearApi(
      (schoolYear) {
        response = schoolYear;
      },
      (errorApiModel) {
        throw errorApiModel;
      },
    );
    return response;
  }

  @override
  Future<RequestEnumValuesApiModel> getRequestEnumValues() async {
    try {
      late RequestEnumValuesApiModel response;
      await homeApiManager.getRequestEnumValuesApi(
        (enumValues) => response = enumValues,
        (error) => throw error,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<CertificateResponse> getCertificates(int schoolYearId) async {
    try {
      late CertificateResponse response;
      await homeApiManager.getCertificatesApi(
        schoolYearId,
        (certificates) {
          response = certificates;
        },
        (error) {
          throw error;
        },
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RequestResponse> getMyRequests(RequestSearchModel searchModel) async {
    try {
      late RequestResponse response;
      await homeApiManager.getMyRequestsApi(
        searchModel.toJson(),
        (requests) => response = requests,
        (error) => throw error,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<GuardianApiModel> getGuardianByUserId() async {
    late GuardianApiModel response;
    await homeApiManager.getGuardianByUserIdApi(
      (guardian) {
        response = guardian;
      },
      (errorApiModel) {
        throw errorApiModel;
      },
    );
    return response;
  }
}
