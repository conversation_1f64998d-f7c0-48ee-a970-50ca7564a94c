import 'package:daleel/apis/api_keys.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/current_school_year_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_enums_value_api_model.dart';

import 'package:daleel/features/main_taps/screens/home/<USER>/models/guardian_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/Entites/certificate_ui_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/Entites/request_ui_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_current_school_year_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_request_enum_values_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_certificates_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_my_requests_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_guardian_by_user_id_usecase.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final GetRequestEnumValuesUseCase getRequestEnumValuesUseCase;
  final GetCurrentSchoolYearUseCase getCurrentSchoolYearUseCase;
  final GetCertificatesUseCase getCertificatesUseCase;
  final GetMyRequestsUseCase getMyRequestsUseCase;
  final GetGuardianByUserIdUseCase getGuardianByUserIdUseCase;

  HomeBloc({
    required this.getRequestEnumValuesUseCase,
    required this.getCurrentSchoolYearUseCase,
    required this.getCertificatesUseCase,
    required this.getMyRequestsUseCase,
    required this.getGuardianByUserIdUseCase,
  }) : super(HomeInitialState()) {
    on<LoadHomeDataEvent>(_onLoadHomeData);
    on<LoadCertificatesEvent>(_onLoadCertificates);
    on<LoadRequestsEvent>(_onLoadRequests);
    on<SearchRequestsEvent>(_onSearchRequests);
    on<ResetRequestsEvent>(_onResetRequests);

    on<LoadGuardianEvent>(_onLoadGuardian);
    on<UpdateRequestTypeSelectionEvent>(_onUpdateRequestTypeSelection);
    on<UpdateRequestStatusSelectionEvent>(_onUpdateRequestStatusSelection);
    on<ResetFiltersEvent>(_onResetFilters);
  }

  Future<void> _onLoadHomeData(
    LoadHomeDataEvent event,
    Emitter<HomeState> emit,
  ) async {
    if (state is HomeInitialState || state is HomeLoadedState) {
      RequestEnumValuesApiModel? enumValues;
      CurrentSchoolYearApiModel? schoolYear;

      try {
        // Get enum values
        final enumValuesResult = await getRequestEnumValuesUseCase(NoParams());
        enumValuesResult.fold(
          (error) {
            // Handle error silently, enumValues will remain null
          },
          (data) {
            enumValues = data;
          },
        );

        // Get school year
        final schoolYearResult = await getCurrentSchoolYearUseCase(NoParams());
        schoolYearResult.fold(
          (error) {
            // Handle error silently, schoolYear will remain null
          },
          (data) {
            schoolYear = data;
          },
        );
      } catch (e) {
        // Handle error silently, both will remain null
      }

      // Emit initial loaded state with loading flags
      emit(
        HomeLoadedState(
          requestEnumValues: enumValues,
          currentSchoolYear: schoolYear,
          certificates: [],
          isCertificatesLoading: true,
          isRequestsLoading: true,
        ),
      );

      // Load certificates and requests separately
      add(const LoadCertificatesEvent());
      add(const LoadRequestsEvent());
    }
  }

  Future<void> _onLoadCertificates(
    LoadCertificatesEvent event,
    Emitter<HomeState> emit,
  ) async {
    if (state is HomeLoadedState) {
      final currentState = state as HomeLoadedState;
      // Show loading for certificates
      emit(currentState.copyWith(isCertificatesLoading: true));

      try {
        final schoolYearId = currentState.currentSchoolYear?.id;

        if (schoolYearId != null) {
          final certificatesResult = await getCertificatesUseCase(schoolYearId);

          certificatesResult.fold(
            (error) {
              // Get fresh state before emit
              final freshState = state as HomeLoadedState;
              emit(
                freshState.copyWith(
                  isCertificatesLoading: false,
                  certificates: [],
                ),
              );
            },
            (certificateListUiData) {
              final certificates =
                  certificateListUiData.certificates.take(4).toList();
              // Get fresh state before emit
              final freshState = state as HomeLoadedState;
              emit(
                freshState.copyWith(
                  isCertificatesLoading: false,
                  certificates: certificates,
                ),
              );
            },
          );
        } else {
          // Get fresh state before emit
          final freshState = state as HomeLoadedState;
          emit(
            freshState.copyWith(isCertificatesLoading: false, certificates: []),
          );
        }
      } catch (e) {
        // Get fresh state before emit
        final freshState = state as HomeLoadedState;
        emit(
          freshState.copyWith(isCertificatesLoading: false, certificates: []),
        );
      }
    }
  }

  Future<void> _onLoadRequests(
    LoadRequestsEvent event,
    Emitter<HomeState> emit,
  ) async {
    if (state is HomeLoadedState) {
      final currentState = state as HomeLoadedState;
      // Show loading for requests (don't touch certificates loading state)
      emit(currentState.copyWith(isRequestsLoading: true));
      try {
        // Get current school year ID and parent ID
        final schoolYearId = currentState.currentSchoolYear?.id ?? 1038;
        final parentId =
            currentState.guardian?.id ?? 2; // Use guardian ID if available

        final searchModel = _createRequestSearchModel(
          schoolYearId: schoolYearId,
          parentId: parentId,
          requestStatus: event.requestStatus,
          requestType: event.requestType,
          keyWord: event.keyWord,
          page: event.page,
          pageSize: event.pageSize,
        );

        final result = await getMyRequestsUseCase(searchModel);

        result.fold(
          (error) {
            // Get fresh state before emit
            final freshState = state as HomeLoadedState;
            emit(freshState.copyWith(isRequestsLoading: false, requests: []));
          },
          (requestResponse) {
            final updatedRequests = _processRequestsWithExpandState(
              requestResponse.requests,
            );
            // Get fresh state before emit
            final freshState = state as HomeLoadedState;
            emit(
              freshState.copyWith(
                isRequestsLoading: false,
                requests: updatedRequests,
              ),
            );
          },
        );
      } catch (e) {
        // Get fresh state before emit
        final freshState = state as HomeLoadedState;
        emit(freshState.copyWith(isRequestsLoading: false, requests: []));
      }
    }
  }

  Future<void> _onSearchRequests(
    SearchRequestsEvent event,
    Emitter<HomeState> emit,
  ) async {
    // Use LoadRequestsEvent with search parameters
    add(
      LoadRequestsEvent(
        requestStatus: event.requestStatus,
        requestType: event.requestType,
        keyWord: event.keyWord,
        page: 1,
        pageSize: 5,
      ),
    );
  }

  Future<void> _onResetRequests(
    ResetRequestsEvent event,
    Emitter<HomeState> emit,
  ) async {
    // Load initial requests with default parameters
    add(const LoadRequestsEvent());
  }

  Future<void> _onLoadGuardian(
    LoadGuardianEvent event,
    Emitter<HomeState> emit,
  ) async {
    final result = await getGuardianByUserIdUseCase(NoParams());

    result.fold(
      (failure) {
        // Handle error silently - guardian is optional data
      },
      (guardian) {
        if (state is HomeLoadedState) {
          final currentState = state as HomeLoadedState;
          emit(currentState.copyWith(guardian: guardian));
        }
      },
    );
  }

  // Helper Methods for cleaner code
  List<RequestUiModel> _processRequestsWithExpandState(
    List<RequestUiModel> requests,
  ) {
    return requests; // No need to modify expand state anymore
  }

  RequestSearchModel _createRequestSearchModel({
    required int schoolYearId,
    required int parentId,
    List<int>? requestStatus,
    List<int>? requestType,
    String? keyWord,
    int page = ApiKeys.onePageValue,
    int pageSize = ApiKeys.requestsPageValue,
  }) {
    return RequestSearchModel(
      requestStatus: requestStatus ?? [],
      requestType: requestType ?? [],
      keyWord: keyWord ?? '',
      page: page,
      pageSize: pageSize,
      sortBy: ApiKeys.sortBy,
      sortColumn: '',
      sortColumnName: ApiKeys.sortColumnName,
      sortDirection: '',
      curriculumId: null,
      gradeId: null,
      userRequestType: null,
      parentId: [parentId],
      schoolYearId: schoolYearId,
    );
  }

  void _onUpdateRequestTypeSelection(
    UpdateRequestTypeSelectionEvent event,
    Emitter<HomeState> emit,
  ) {
    if (state is HomeLoadedState) {
      final currentState = state as HomeLoadedState;
      emit(
        currentState.copyWith(selectedRequestType: event.selectedRequestType),
      );
    }
  }

  void _onUpdateRequestStatusSelection(
    UpdateRequestStatusSelectionEvent event,
    Emitter<HomeState> emit,
  ) {
    if (state is HomeLoadedState) {
      final currentState = state as HomeLoadedState;
      emit(
        currentState.copyWith(
          selectedRequestStatus: event.selectedRequestStatus,
        ),
      );
    }
  }

  void _onResetFilters(ResetFiltersEvent event, Emitter<HomeState> emit) {
    if (state is HomeLoadedState) {
      final currentState = state as HomeLoadedState;
      emit(
        currentState.copyWith(
          clearSelectedRequestType: true,
          clearSelectedRequestStatus: true,
        ),
      );
      // Also trigger reset of requests
      add(ResetRequestsEvent());
    }
  }
}
