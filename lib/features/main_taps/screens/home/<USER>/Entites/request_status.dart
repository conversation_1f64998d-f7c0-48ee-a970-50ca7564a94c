import 'package:daleel/utils/locale/app_localization_keys.dart';

enum RequestStatus {
  pending,
  approved,
  rejected,
  completed;

  static RequestStatus fromApiKey(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return pending;
      case 'approved':
        return approved;
      case 'rejected':
        return rejected;
      case 'completed':
        return completed;
      default:
        return pending;
    }
  }

  String getStatusName() {
    switch (this) {
      case pending:
        return LocalizationKeys.pending;
      case approved:
        return LocalizationKeys.approved;
      case rejected:
        return LocalizationKeys.rejected;
      case completed:
        return LocalizationKeys.completed;
    }
  }

  String getDisplayStatus() {
    switch (this) {
      case pending:
        return LocalizationKeys.pendingReview;
      case approved:
        return LocalizationKeys.approvedStatus;
      case rejected:
        return LocalizationKeys.rejectedStatus;
      case completed:
        return LocalizationKeys.completedStatus;
    }
  }
}
