import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/Entites/request_status.dart';

class RequestUiModel {
  final int instanceId;
  final String requestNumber;
  final String requestType;
  final String requestStatus;
  final String? certificateStatus;
  final String createdByName;
  final DateTime createdDate;
  final DateTime? lastUpdatedDate;
  final String relatedSonName;
  final String schoolName;
  final String curriculumName;
  final bool isRecent; // UI-specific property
  final RequestStatus displayStatus; // Formatted status for UI

  const RequestUiModel({
    required this.instanceId,
    required this.requestNumber,
    required this.requestType,
    required this.requestStatus,
    this.certificateStatus,
    required this.createdByName,
    required this.createdDate,
    this.lastUpdatedDate,
    required this.relatedSonName,
    required this.schoolName,
    required this.curriculumName,
    this.isRecent = false,
    required this.displayStatus,
  });

  factory RequestUiModel.fromRequestApiModel(RequestApiModel apiModel) {
    return RequestUiModel(
      instanceId: apiModel.instanceId,
      requestNumber: apiModel.requestNumber,
      requestType: apiModel.requestType,
      requestStatus: apiModel.requestStatus,
      certificateStatus: apiModel.certificateStatus,
      createdByName: apiModel.createdBy.en,
      createdDate: DateTime.parse(apiModel.createdDate),
      lastUpdatedDate:
          apiModel.lastUpdatedDate != null
              ? DateTime.parse(apiModel.lastUpdatedDate!)
              : null,
      relatedSonName: apiModel.relatedSon.name.en,
      schoolName: apiModel.school.name.en,
      curriculumName: apiModel.curriculum.name.en,
      isRecent: _isRecentRequest(apiModel.createdDate),
      displayStatus: RequestStatus.fromApiKey(apiModel.requestStatus),
    );
  }

  static bool _isRecentRequest(String createdDateString) {
    try {
      final createdDate = DateTime.parse(createdDateString);
      final now = DateTime.now();
      final difference = now.difference(createdDate).inDays;
      return difference <= 7; // Consider requests from last 7 days as recent
    } catch (e) {
      return false;
    }
  }

  RequestUiModel copyWith({
    int? instanceId,
    String? requestNumber,
    String? requestType,
    String? requestStatus,
    String? certificateStatus,
    String? createdByName,
    DateTime? createdDate,
    DateTime? lastUpdatedDate,
    String? relatedSonName,
    String? schoolName,
    String? curriculumName,
    bool? isRecent,
    RequestStatus? displayStatus,
  }) {
    return RequestUiModel(
      instanceId: instanceId ?? this.instanceId,
      requestNumber: requestNumber ?? this.requestNumber,
      requestType: requestType ?? this.requestType,
      requestStatus: requestStatus ?? this.requestStatus,
      certificateStatus: certificateStatus ?? this.certificateStatus,
      createdByName: createdByName ?? this.createdByName,
      createdDate: createdDate ?? this.createdDate,
      lastUpdatedDate: lastUpdatedDate ?? this.lastUpdatedDate,
      relatedSonName: relatedSonName ?? this.relatedSonName,
      schoolName: schoolName ?? this.schoolName,
      curriculumName: curriculumName ?? this.curriculumName,
      isRecent: isRecent ?? this.isRecent,
      displayStatus: displayStatus ?? this.displayStatus,
    );
  }
}

class RequestListUiData {
  final int totalAllData;
  final int total;
  final List<RequestUiModel> requests;

  const RequestListUiData({
    required this.totalAllData,
    required this.total,
    required this.requests,
  });

  factory RequestListUiData.fromRequestResponse(RequestResponse response) {
    return RequestListUiData(
      totalAllData: response.totalAllData,
      total: response.total,
      requests:
          response.data
              .map((apiModel) => RequestUiModel.fromRequestApiModel(apiModel))
              .toList(),
    );
  }
}
