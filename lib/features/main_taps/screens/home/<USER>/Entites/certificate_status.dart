import 'package:daleel/utils/locale/app_localization_keys.dart';

enum CertificateStatus {
  pending,
  approved,
  completed,
  rejected,
  paid,
  payed,
  paymentStarted,
  free;

  static CertificateStatus fromApiKey(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return pending;
      case 'approved':
        return approved;
      case 'completed':
        return completed;
      case 'rejected':
        return rejected;
      case 'paid':
        return paid;
      case 'payed':
        return payed;
      case 'paymentstarted':
        return paymentStarted;
      default:
        return pending;
    }
  }

  String getStatusName() {
    switch (this) {
      case pending:
        return LocalizationKeys.pending;
      case approved:
        return LocalizationKeys.approved;
      case completed:
        return LocalizationKeys.completed;
      case rejected:
        return LocalizationKeys.rejected;
      case paid:
      case payed:
        return LocalizationKeys.paid;
      case paymentStarted:
        return LocalizationKeys.pay;
      case free:
        return LocalizationKeys.free;
    }
  }

  String getDisplayStatus() {
    switch (this) {
      case pending:
        return LocalizationKeys.pendingProcessing;
      case approved:
        return LocalizationKeys.approvedStatus;
      case completed:
        return LocalizationKeys.readyForDownload;
      case rejected:
        return LocalizationKeys.rejectedStatus;
      case paid:
      case payed:
        return LocalizationKeys.paidStatus;
      case paymentStarted:
        return LocalizationKeys.paymentRequired;
      case free:
        return LocalizationKeys.free;
    }
  }

  String getButtonText() {
    switch (this) {
      case payed:
        return LocalizationKeys.view;
      case paymentStarted:
        return LocalizationKeys.pay;
      case pending:
        return LocalizationKeys.pending;
      case rejected:
        return LocalizationKeys.rejected;
      default:
        return LocalizationKeys.pay;
    }
  }
}
