class CurrentSchoolYearApiModel {
  final int id;
  final Name name;

  CurrentSchoolYearApiModel({required this.id, required this.name});

  factory CurrentSchoolYearApiModel.fromJson(Map<String, dynamic> json) {
    return CurrentSchoolYearApiModel(
      id: json['id'] as int,
      name: Name.fromJson(json['name']),
    );
  }
}

class Name {
  final String en;
  final String ar;

  Name({required this.en, required this.ar});

  factory Name.fromJson(Map<String, dynamic> json) {
    return Name(en: json['en'] as String, ar: json['ar'] as String);
  }
}
