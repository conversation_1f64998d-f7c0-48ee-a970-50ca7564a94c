class RequestEnumValuesApiModel {
  final List<RequestStatus> requestTypes;
  final List<RequestStatus> requestStatuses;
  final List<RequestStatus> semesters;

  RequestEnumValuesApiModel({
    required this.requestTypes,
    required this.requestStatuses,
    required this.semesters,
  });

  factory RequestEnumValuesApiModel.fromJson(Map<String, dynamic> json) {
    return RequestEnumValuesApiModel(
      requestTypes:
          (json['requestTypes'] as List<dynamic>)
              .map((e) => RequestStatus.fromJson(e))
              .toList(),
      requestStatuses:
          (json['requestStatuses'] as List<dynamic>)
              .map((e) => RequestStatus.fromJson(e))
              .toList(),
      semesters:
          (json['semesters'] as List<dynamic>)
              .map((e) => RequestStatus.fromJson(e))
              .toList(),
    );
  }
}

class RequestStatus {
  final int id;
  final String name;

  RequestStatus({required this.id, required this.name});

  factory RequestStatus.fromJson(Map<String, dynamic> json) {
    return RequestStatus(id: json['id'] as int, name: json['name'] as String);
  }
}
