class RequestResponse {
  final int totalAllData;
  final int total;
  final List<RequestApiModel> data;

  RequestResponse({
    required this.totalAllData,
    required this.total,
    required this.data,
  });

  factory RequestResponse.fromJson(Map<String, dynamic> json) {
    return RequestResponse(
      totalAllData: json['totalAllData'] ?? 0,
      total: json['total'] ?? 0,
      data:
          (json['data'] as List<dynamic>?)
              ?.map((item) => RequestApiModel.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalAllData': totalAllData,
      'total': total,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }
}

class RequestApiModel {
  final int instanceId;
  final String requestNumber;
  final String requestType;
  final String requestStatus;
  final String? certificateStatus;
  final RequestName createdBy;
  final String createdDate;
  final String? lastUpdatedDate;
  final RelatedSon relatedSon;
  final dynamic schoolCreator;
  final School school;
  final dynamic grade;
  final Curriculum curriculum;
  bool isExpanded;

  RequestApiModel({
    required this.instanceId,
    required this.requestNumber,
    required this.requestType,
    required this.requestStatus,
    this.certificateStatus,
    required this.createdBy,
    required this.createdDate,
    this.lastUpdatedDate,
    required this.relatedSon,
    this.schoolCreator,
    required this.school,
    this.grade,
    required this.curriculum,
    this.isExpanded = false,
  });

  factory RequestApiModel.fromJson(Map<String, dynamic> json) {
    return RequestApiModel(
      instanceId: json['instanceId'] ?? 0,
      requestNumber: json['requestNumber'] ?? '',
      requestType: json['requestType'] ?? '',
      requestStatus: json['requestStatus'] ?? '',
      certificateStatus: json['certificateStatus'],
      createdBy: RequestName.fromJson(json['createdBy'] ?? {}),
      createdDate: json['createdDate'] ?? '',
      lastUpdatedDate: json['lastUpdatedDate'],
      relatedSon: RelatedSon.fromJson(json['relatedSon'] ?? {}),
      schoolCreator: json['schoolCreator'],
      school: School.fromJson(json['school'] ?? {}),
      grade: json['grade'],
      curriculum: Curriculum.fromJson(json['curriculum'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'instanceId': instanceId,
      'requestNumber': requestNumber,
      'requestType': requestType,
      'requestStatus': requestStatus,
      'certificateStatus': certificateStatus,
      'createdBy': createdBy.toJson(),
      'createdDate': createdDate,
      'lastUpdatedDate': lastUpdatedDate,
      'relatedSon': relatedSon.toJson(),
      'schoolCreator': schoolCreator,
      'school': school.toJson(),
      'grade': grade,
      'curriculum': curriculum.toJson(),
    };
  }

  RequestApiModel copyWith({
    int? instanceId,
    String? requestNumber,
    String? requestType,
    String? requestStatus,
    String? certificateStatus,
    RequestName? createdBy,
    String? createdDate,
    String? lastUpdatedDate,
    RelatedSon? relatedSon,
    dynamic schoolCreator,
    School? school,
    dynamic grade,
    Curriculum? curriculum,
    bool? isExpanded,
  }) {
    return RequestApiModel(
      instanceId: instanceId ?? this.instanceId,
      requestNumber: requestNumber ?? this.requestNumber,
      requestType: requestType ?? this.requestType,
      requestStatus: requestStatus ?? this.requestStatus,
      certificateStatus: certificateStatus ?? this.certificateStatus,
      createdBy: createdBy ?? this.createdBy,
      createdDate: createdDate ?? this.createdDate,
      lastUpdatedDate: lastUpdatedDate ?? this.lastUpdatedDate,
      relatedSon: relatedSon ?? this.relatedSon,
      schoolCreator: schoolCreator ?? this.schoolCreator,
      school: school ?? this.school,
      grade: grade ?? this.grade,
      curriculum: curriculum ?? this.curriculum,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}

class RequestName {
  final String en;
  final String ar;

  RequestName({required this.en, required this.ar});

  factory RequestName.fromJson(Map<String, dynamic> json) {
    return RequestName(en: json['en'] ?? '', ar: json['ar'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'en': en, 'ar': ar};
  }
}

class RelatedSon {
  final int id;
  final RequestName name;

  RelatedSon({required this.id, required this.name});

  factory RelatedSon.fromJson(Map<String, dynamic> json) {
    return RelatedSon(
      id: json['id'] ?? 0,
      name: RequestName.fromJson(json['name'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name.toJson()};
  }
}

class School {
  final int id;
  final RequestName name;

  School({required this.id, required this.name});

  factory School.fromJson(Map<String, dynamic> json) {
    return School(
      id: json['id'] ?? 0,
      name: RequestName.fromJson(json['name'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name.toJson()};
  }
}

class Curriculum {
  final int id;
  final RequestName name;

  Curriculum({required this.id, required this.name});

  factory Curriculum.fromJson(Map<String, dynamic> json) {
    return Curriculum(
      id: json['id'] ?? 0,
      name: RequestName.fromJson(json['name'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name.toJson()};
  }
}

class RequestSearchModel {
  final List<int> requestStatus;
  final List<int> requestType;
  final String keyWord;
  final int page;
  final int pageSize;
  final String sortBy;
  final String sortColumn;
  final String sortColumnName;
  final String sortDirection;
  final int? curriculumId;
  final int? gradeId;
  final int? userRequestType;
  final List<int> parentId;
  final int schoolYearId;

  RequestSearchModel({
    required this.requestStatus,
    required this.requestType,
    required this.keyWord,
    required this.page,
    required this.pageSize,
    required this.sortBy,
    required this.sortColumn,
    required this.sortColumnName,
    required this.sortDirection,
    this.curriculumId,
    this.gradeId,
    this.userRequestType,
    required this.parentId,
    required this.schoolYearId,
  });

  Map<String, dynamic> toJson() {
    return {
      'RequestStatus': requestStatus,
      'requestType': requestType,
      'KeyWord': keyWord,
      'Page': page,
      'PageSize': pageSize,
      'SortBy': sortBy,
      'SortColumn': sortColumn,
      'SortColumnName': sortColumnName,
      'SortDirection': sortDirection,
      'curriculumId': curriculumId,
      'gradeId': gradeId,
      'userRequestType': userRequestType,
      'parentId': parentId,
      'schoolYearId': schoolYearId,
    };
  }
}
