class CertificateResponse {
  final int totalAllData;
  final int total;
  final List<CertificateApiModel> data;

  CertificateResponse({
    required this.totalAllData,
    required this.total,
    required this.data,
  });

  factory CertificateResponse.fromJson(Map<String, dynamic> json) {
    return CertificateResponse(
      totalAllData: json['totalAllData'] ?? 0,
      total: json['total'] ?? 0,
      data:
          (json['data'] as List<dynamic>?)
              ?.map((item) => CertificateApiModel.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalAllData': totalAllData,
      'total': total,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }
}

class CertificateApiModel {
  final int id;
  final int commonCertificateRequestId;
  final int schoolYearId;
  final Name certificateName;
  final String certificateType;
  final int studentId;
  final Name studentName;
  final String status;
  final double fees;
  final String? url;
  final String? jsonObj;
  final String? paymentRefNo;
  final bool createdBySpeaAdmin;
  final String? paymentReceiptNo;
  final String? certificateIssuanceDate;

  CertificateApiModel({
    required this.id,
    required this.commonCertificateRequestId,
    required this.schoolYearId,
    required this.certificateName,
    required this.certificateType,
    required this.studentId,
    required this.studentName,
    required this.status,
    required this.fees,
    this.url,
    this.jsonObj,
    this.paymentRefNo,
    required this.createdBySpeaAdmin,
    this.paymentReceiptNo,
    this.certificateIssuanceDate,
  });

  factory CertificateApiModel.fromJson(Map<String, dynamic> json) {
    return CertificateApiModel(
      id: json['id'] ?? 0,
      commonCertificateRequestId: json['commonCertificateRequestId'] ?? 0,
      schoolYearId: json['schoolYearId'] ?? 0,
      certificateName: Name.fromJson(json['certificateName'] ?? {}),
      certificateType: json['certificateType'] ?? '',
      studentId: json['studentId'] ?? 0,
      studentName: Name.fromJson(json['studentName'] ?? {}),
      status: json['status'] ?? '',
      fees: (json['fees'] ?? 0).toDouble(),
      url: json['url'],
      jsonObj: json['jsonObj'],
      paymentRefNo: json['paymentRefNo'],
      createdBySpeaAdmin: json['createdBySpeaAdmin'] ?? false,
      paymentReceiptNo: json['paymentReceiptNo'],
      certificateIssuanceDate: json['certificateIssuanceDate'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'commonCertificateRequestId': commonCertificateRequestId,
      'schoolYearId': schoolYearId,
      'certificateName': certificateName.toJson(),
      'certificateType': certificateType,
      'studentId': studentId,
      'studentName': studentName.toJson(),
      'status': status,
      'fees': fees,
      'url': url,
      'jsonObj': jsonObj,
      'paymentRefNo': paymentRefNo,
      'createdBySpeaAdmin': createdBySpeaAdmin,
      'paymentReceiptNo': paymentReceiptNo,
      'certificateIssuanceDate': certificateIssuanceDate,
    };
  }
}

class Name {
  final String en;
  final String ar;

  Name({required this.en, required this.ar});

  factory Name.fromJson(Map<String, dynamic> json) {
    return Name(en: json['en'] ?? '', ar: json['ar'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'en': en, 'ar': ar};
  }
}
