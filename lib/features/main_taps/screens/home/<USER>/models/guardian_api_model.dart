class GuardianApiModel {
  final int id;
  final GuardianName name;

  GuardianApiModel({required this.id, required this.name});

  factory GuardianApiModel.fromJson(Map<String, dynamic> json) {
    return GuardianApiModel(
      id: json['id'] as int,
      name: GuardianName.from<PERSON>son(json['name'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name.toJson()};
  }
}

class GuardianName {
  final String en;
  final String ar;

  GuardianName({required this.en, required this.ar});

  factory GuardianName.fromJson(Map<String, dynamic> json) {
    return GuardianName(en: json['en'] as String, ar: json['ar'] as String);
  }

  Map<String, dynamic> toJson() {
    return {'en': en, 'ar': ar};
  }
}
