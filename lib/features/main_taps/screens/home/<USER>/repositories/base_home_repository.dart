import 'package:daleel/features/main_taps/screens/home/<USER>/models/current_school_year_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_enums_value_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/certificate_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/guardian_api_model.dart';

abstract class BaseHomeRepository {
  Future<CurrentSchoolYearApiModel> getCurrentSchoolYear();
  Future<RequestEnumValuesApiModel> getRequestEnumValues();
  Future<CertificateResponse> getCertificates(int schoolYearId);
  Future<RequestResponse> getMyRequests(RequestSearchModel searchModel);
  Future<GuardianApiModel> getGuardianByUserId();
}
