import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:daleel/features/profile/presentation/screen/profile_screen.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class UserHeader extends StatefulWidget {
  const UserHeader({super.key});

  @override
  State<UserHeader> createState() => _UserHeaderState();
}

class _UserHeaderState extends State<UserHeader> {
  final UserMangers userManager = GetIt.I<UserMangers>();

  String userName = '';
  String userEmail = '';
  String userImage = '';

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    await userManager.setUserInfoAndModeFromLocal();

    if (mounted) {
      setState(() {
        final isEnglish = context.isEnglish;
        userName = userManager.getUserName(isEnglish);
        if (userName.isEmpty) {
          userName = userManager.getUserName(!isEnglish);
        }
        userEmail = userManager.getEmail();
        userImage = userManager.getImageUrl();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(40),
          bottomRight: Radius.circular(40),
        ),
        image: DecorationImage(
          image: AssetImage(AppAssetPaths.background_header),
          fit: BoxFit.cover,
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Padding(
        padding: const EdgeInsets.only(top: 40),
        child: Row(
          children: [
            GestureDetector(
              onTap: () {
                context.go(ProfileScreen.routeName);
              },
              child: CircleAvatar(
                radius: 30,
                backgroundImage:
                    userImage.isNotEmpty && userImage != 'null'
                        ? NetworkImage(userImage)
                        : const AssetImage(AppAssetPaths.ic_user_icon)
                            as ImageProvider,
                onBackgroundImageError:
                    userImage.isNotEmpty && userImage != 'null'
                        ? (exception, stackTrace) {
                          setState(() {
                            userImage = '';
                          });
                        }
                        : null,
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    userName.isNotEmpty
                        ? '${context.translate(LocalizationKeys.hello)} $userName'
                        : context.translate(LocalizationKeys.welcomeUser),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.whiteText,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 5),
                  Text(
                    userEmail.isNotEmpty
                        ? userEmail
                        : context.translate(LocalizationKeys.defaultEmail),
                    style: const TextStyle(fontSize: 12, color: AppColors.whiteText),
                  ),
                ],
              ),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildIcon(Icons.calendar_month),
                const SizedBox(width: 10),
                _buildIcon(Icons.language),
                const SizedBox(width: 10),
                _buildIcon(Icons.notifications),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIcon(IconData icon) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.whiteIcon.withValues(alpha: 0.1),
      ),
      padding: const EdgeInsets.all(10),
      child: Icon(icon, color: AppColors.whiteText),
    );
  }
}
