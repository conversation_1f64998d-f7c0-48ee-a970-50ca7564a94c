import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/Entites/request_ui_model.dart';
import 'package:daleel/features/widgets/expandable_widget.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/format/app_date_format.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RequestItemWidget extends StatelessWidget {
  final RequestUiModel request;
  final bool initExpanded;

  const RequestItemWidget({
    super.key,
    required this.request,
    this.initExpanded = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: AppColors.colorCertificateBackground,
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
      child: ExpandableWidget(
        borderRadius: 8,
        initExpanded: initExpanded,
        collapsedWidget: _buildCollapsedContent(context),
        expandedWidget: _buildExpandedContent(context),
      ),
    );
  }

  Widget _buildCollapsedContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Image.asset(AppAssetPaths.ic_request_icon, width: 24, height: 24),
          SizedBox(width: 20.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  request.requestType,
                  style: const TextStyle(
                    color: AppColors.appBarBackground,
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          ExpandableIcon(
            theme: const ExpandableThemeData(
              expandIcon: Icons.expand_more,
              collapseIcon: Icons.expand_less,
              iconColor: AppColors.appBarBackground,
              iconSize: 24,
              iconRotationAngle: 0,
              iconPadding: EdgeInsets.zero,
              hasIcon: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header (same as collapsed)
          Row(
            children: [
              Image.asset(AppAssetPaths.ic_request_icon, width: 24, height: 24),
              SizedBox(width: 20.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      request.requestType,
                      style: const TextStyle(
                        color: AppColors.appBarBackground,
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              ExpandableIcon(
                theme: const ExpandableThemeData(
                  expandIcon: Icons.expand_more,
                  collapseIcon: Icons.expand_less,
                  iconColor: AppColors.appBarBackground,
                  iconSize: 24,
                  iconRotationAngle: 0,
                  iconPadding: EdgeInsets.zero,
                  hasIcon: true,
                ),
              ),
            ],
          ),

          // Expanded details
          const Divider(color: AppColors.mainGray, height: 24),
          _buildDetailRow(
            context.translate(LocalizationKeys.requestNumber),
            request.requestNumber,
            false,
            true,
            context,
          ),
          _buildDetailRow(
            context.translate(LocalizationKeys.requestRelatedTo),
            request.relatedSonName,
            false,
            false,
            context,
          ),
          _buildDetailRow(
            context.translate(LocalizationKeys.school),
            request.schoolName,
            false,
            false,
            context,
          ),
          _buildDetailRow(
            context.translate(LocalizationKeys.curriculum),
            request.curriculumName,
            false,
            false,
            context,
          ),
          _buildDetailRow(
            context.translate(LocalizationKeys.createdBy),
            request.createdByName,
            false,
            false,
            context,
          ),
          _buildDetailRow(
            context.translate(LocalizationKeys.requestDate),
            AppDateFormat.formattingInvitationDisplayDate(
              request.createdDate,
              context.languageCode,
            ),
            false,
            false,
            context,
          ),
          _buildDetailRow(
            context.translate(LocalizationKeys.modificationDate),
            request.lastUpdatedDate != null
                ? AppDateFormat.formattingInvitationDisplayDate(
                  request.lastUpdatedDate!,
                  context.languageCode,
                )
                : context.translate(LocalizationKeys.notAvailable),
            true,
            false,
            context,
          ),
          SizedBox(height: 8.h),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value,
    bool isDetails,
    bool isStatus,
    BuildContext context,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 12,
                color: AppColors.mainGray,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 12,
                color: AppColors.mainGray,
              ),
            ),
          ),
          isStatus
              ? Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppColors.blueGradient.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  context.translate(LocalizationKeys.pending),
                  style: TextStyle(fontSize: 12, color: AppColors.statusHome),
                ),
              )
              : const SizedBox.shrink(),
          isDetails
              ? Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.colorSecondary),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  context.translate(LocalizationKeys.details),
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    color: AppColors.colorSecondary,
                    fontSize: 12,
                  ),
                ),
              )
              : const SizedBox.shrink(),
        ],
      ),
    );
  }
}
