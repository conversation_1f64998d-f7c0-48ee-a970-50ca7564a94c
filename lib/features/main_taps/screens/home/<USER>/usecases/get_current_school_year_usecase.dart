import 'package:daleel/features/main_taps/screens/home/<USER>/models/current_school_year_api_model.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repositories/base_home_repository.dart';
import 'package:get_it/get_it.dart';

import '../../../../../../preferences/preferences_manager.dart';

class GetCurrentSchoolYearUseCase
    extends BaseUseCase<CurrentSchoolYearApiModel, NoParams> {
  final BaseHomeRepository homeRepository;
  final PreferencesManager preferencesManager;
  GetCurrentSchoolYearUseCase(this.homeRepository, this.preferencesManager);

  @override
  Future<Either<ErrorApiModel, CurrentSchoolYearApiModel>> call(
    NoParams params,
  ) async {
    late Either<ErrorApiModel, CurrentSchoolYearApiModel> state;
    await homeRepository
        .getCurrentSchoolYear()
        .then((schoolYearResponse) {
          state = Right(schoolYearResponse);
          GetIt.I<UserMangers>().setSchoolYearId(schoolYearResponse.id);
        })
        .catchError((onError) {
          ErrorApiModel errorApiModel = onError as ErrorApiModel;
          state = Left(errorApiModel);
        });
    return state;
  }
}
