import 'package:daleel/logic/user_manager.dart';
import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/guardian_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repositories/base_home_repository.dart';
import 'package:get_it/get_it.dart';

import '../../../../../../preferences/preferences_manager.dart';

class GetGuardianByUserIdUseCase
    extends BaseUseCase<GuardianApiModel, NoParams> {
  final BaseHomeRepository homeRepository;
  final PreferencesManager preferencesManager;
  GetGuardianByUserIdUseCase(this.homeRepository, this.preferencesManager);

  @override
  Future<Either<ErrorApiModel, GuardianApiModel>> call(NoParams params) async {
    late Either<ErrorApiModel, GuardianApiModel> state;
    await homeRepository
        .getGuardianByUserId()
        .then((guardianResponse) {
          state = Right(guardianResponse);
          GetIt.I<UserMangers>().setGuardianId(guardianResponse.id);
        })
        .catchError((onError) {
          ErrorApiModel errorApiModel = onError as ErrorApiModel;
          state = Left(errorApiModel);
        });
    return state;
  }
}
