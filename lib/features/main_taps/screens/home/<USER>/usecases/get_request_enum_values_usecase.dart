import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_enums_value_api_model.dart';
import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repositories/base_home_repository.dart';

class GetRequestEnumValuesUseCase
    extends BaseUseCase<RequestEnumValuesApiModel, NoParams> {
  final BaseHomeRepository homeRepository;

  GetRequestEnumValuesUseCase(this.homeRepository);

  @override
  Future<Either<ErrorApiModel, RequestEnumValuesApiModel>> call(
    NoParams params,
  ) async {
    late Either<ErrorApiModel, RequestEnumValuesApiModel> state;
    await homeRepository
        .getRequestEnumValues()
        .then((enumValuesResponse) {
          state = Right(enumValuesResponse);
        })
        .catchError((onError) {
          ErrorApiModel errorApiModel = onError as ErrorApiModel;
          state = Left(errorApiModel);
        });
    return state;
  }
}
