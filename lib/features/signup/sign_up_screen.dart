import 'package:daleel/utils/feedback/feedback_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../webview/dynamic_web_view.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});
  static const routeName = "/sign_up";

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}
class _SignUpScreenState extends State<SignUpScreen> {
  static const String signUpUrl =
      'https://daleel-spea-front-chdda7awapavdecy.eastus-01.azurewebsites.net/auth/login/registration';
  static const String redirectUrl =
      'https://daleel-spea-front-qa-cpcscfarfndddcbk.eastus-01.azurewebsites.net/signup-success';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: DynamicWebView(
        url: signUpUrl,
        redirectUrl: redirectUrl,
        onSuccessCallback: () {
          context.pop();
        },
        onFailureCallback: () {
          showSnackBarMassage("Auth Failed", context);
        },
      ),
    );
  }
}

