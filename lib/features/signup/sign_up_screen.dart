import 'package:daleel/utils/feedback/feedback_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../apis/api_urls.dart';
import '../webview/dynamic_web_view.dart';
import '../webview/web_views_urls.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});
  static const routeName = "/sign_up";

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}
class _SignUpScreenState extends State<SignUpScreen> {
  final String webViewUrl = WebViewsUrls.baseWebViewUrl + WebViewsUrls.signUp;
  late final String signUpUrl = webViewUrl;

  static final String redirectUrl = '${WebViewsUrls.baseWebViewUrl}/signup-success';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: DynamicWebView(
        url: signUpUrl,
        redirectUrl: redirectUrl,
        onSuccessCallback: () {
          context.pop();
        },
        onFailureCallback: () {
          showSnackBarMassage("Auth Failed", context);
        },
      ),
    );
  }
}

