import 'package:daleel/features/notifications/data/models/notification_api_model.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:flutter/material.dart';

class NotificationUiModel {
  final int id;
  final String title;
  final String description;
  final String userName;
  final DateTime createdAt;
  final NotificationStatus status;
  final bool isNew;
  final String pageLink;
  final bool hasUserAvatar;

  NotificationUiModel({
    required this.id,
    required this.title,
    required this.description,
    required this.userName,
    required this.createdAt,
    required this.status,
    required this.isNew,
    required this.pageLink,
    required this.hasUserAvatar,
  });

  factory NotificationUiModel.fromApiModel(NotificationApiModel apiModel) {
    DateTime parsedDate;
    try {
      parsedDate = DateTime.parse(apiModel.createdDate);
    } catch (e) {
      parsedDate = DateTime.now();
    }

    final status =
        apiModel.isRead ? NotificationStatus.read : NotificationStatus.unread;

    final isNew = !apiModel.isRead;

    // الافتراضي: العربي الأول ولو مش موجود fallback للإنجليزي
    final langCode = "ar";

    return NotificationUiModel(
      id: apiModel.id,
      title: apiModel.title.getValue(langCode),
      description: apiModel.description.getValue(langCode),
      userName: apiModel.userName.getValue(langCode),
      createdAt: parsedDate,
      status: status,
      isNew: isNew,
      pageLink: apiModel.pageLink,
      hasUserAvatar: apiModel.userName.getValue(langCode).isNotEmpty,
    );
  }

  factory NotificationUiModel.fromApiModelWithLocalization(
    NotificationApiModel apiModel,
    BuildContext context,
  ) {
    DateTime parsedDate;
    try {
      parsedDate = DateTime.parse(apiModel.createdDate);
    } catch (e) {
      parsedDate = DateTime.now();
    }

    final status =
        apiModel.isRead ? NotificationStatus.read : NotificationStatus.unread;

    final isNew = !apiModel.isRead;

    final langCode = context.isEnglish ? "en" : "ar";

    return NotificationUiModel(
      id: apiModel.id,
      title: apiModel.title.getValue(langCode),
      description: apiModel.description.getValue(langCode),
      userName: apiModel.userName.getValue(langCode),
      createdAt: parsedDate,
      status: status,
      isNew: isNew,
      pageLink: apiModel.pageLink,
      hasUserAvatar: apiModel.userName.getValue(langCode).isNotEmpty,
    );
  }

  factory NotificationUiModel.fromApiModelWithLanguage(
    NotificationApiModel apiModel,
    String languageCode,
  ) {
    DateTime parsedDate;
    try {
      parsedDate = DateTime.parse(apiModel.createdDate);
    } catch (e) {
      parsedDate = DateTime.now();
    }

    final status =
        apiModel.isRead ? NotificationStatus.read : NotificationStatus.unread;

    final isNew = !apiModel.isRead;

    return NotificationUiModel(
      id: apiModel.id,
      title: apiModel.title.getValue(languageCode),
      description: apiModel.description.getValue(languageCode),
      userName: apiModel.userName.getValue(languageCode),
      createdAt: parsedDate,
      status: status,
      isNew: isNew,
      pageLink: apiModel.pageLink,
      hasUserAvatar: apiModel.userName.getValue(languageCode).isNotEmpty,
    );
  }

  NotificationUiModel copyWith({
    int? id,
    String? title,
    String? description,
    String? userName,
    DateTime? createdAt,
    NotificationStatus? status,
    bool? isNew,
    String? pageLink,
    bool? hasUserAvatar,
  }) {
    return NotificationUiModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      userName: userName ?? this.userName,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      isNew: isNew ?? this.isNew,
      pageLink: pageLink ?? this.pageLink,
      hasUserAvatar: hasUserAvatar ?? this.hasUserAvatar,
    );
  }
}

enum NotificationStatus { read, unread }

class NotificationListUiData {
  final int totalAllData;
  final int total;
  final List<NotificationUiModel> notifications;

  const NotificationListUiData({
    required this.totalAllData,
    required this.total,
    required this.notifications,
  });

  factory NotificationListUiData.fromNotificationResponse(
    NotificationResponse response,
  ) {
    return NotificationListUiData(
      totalAllData: response.totalAllData,
      total: response.total,
      notifications: response.data
          .map((apiModel) => NotificationUiModel.fromApiModel(apiModel))
          .toList(),
    );
  }

  factory NotificationListUiData.fromNotificationResponseWithContext(
    NotificationResponse response,
    BuildContext context,
  ) {
    return NotificationListUiData(
      totalAllData: response.totalAllData,
      total: response.total,
      notifications: response.data
          .map((apiModel) =>
              NotificationUiModel.fromApiModelWithLocalization(apiModel, context))
          .toList(),
    );
  }

  factory NotificationListUiData.fromNotificationResponseWithLanguage(
    NotificationResponse response,
    String languageCode,
  ) {
    return NotificationListUiData(
      totalAllData: response.totalAllData,
      total: response.total,
      notifications: response.data
          .map((apiModel) =>
              NotificationUiModel.fromApiModelWithLanguage(apiModel, languageCode))
          .toList(),
    );
  }
}