import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/notifications/data/models/notification_api_model.dart';
import 'package:daleel/features/notifications/domain/entities/notification_ui_model.dart';
import 'package:daleel/features/notifications/domain/repositories/base_notifications_repository.dart';

class GetNotificationsUseCase
    extends BaseUseCase<NotificationListUiData, NotificationSearchModel> {
  final BaseNotificationsRepository notificationsRepository;

  GetNotificationsUseCase(this.notificationsRepository);

  @override
  Future<Either<ErrorApiModel, NotificationListUiData>> call(
    NotificationSearchModel params,
  ) async {
    late Either<ErrorApiModel, NotificationListUiData> state;
    await notificationsRepository
        .getNotifications(params)
        .then((notificationsResponse) {
          // Convert API response to UI model
          final uiData = NotificationListUiData.fromNotificationResponse(
            notificationsResponse,
          );
          state = Right(uiData);
        })
        .catchError((onError) {
          ErrorApiModel errorApiModel = onError as ErrorApiModel;
          state = Left(errorApiModel);
        });
    return state;
  }
}
