import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/notifications/data/models/notification_api_model.dart';
import 'package:daleel/features/notifications/domain/entities/notification_ui_model.dart';
import 'package:daleel/features/notifications/domain/repositories/base_notifications_repository.dart';

class GetNotificationsWithLocalizationParams {
  final NotificationSearchModel searchModel;
  final String languageCode;

  GetNotificationsWithLocalizationParams({
    required this.searchModel,
    required this.languageCode,
  });
}

class GetNotificationsWithLocalizationUseCase
    extends BaseUseCase<NotificationListUiData, GetNotificationsWithLocalizationParams> {
  final BaseNotificationsRepository notificationsRepository;

  GetNotificationsWithLocalizationUseCase(this.notificationsRepository);

  @override
  Future<Either<ErrorApiModel, NotificationListUiData>> call(
    GetNotificationsWithLocalizationParams params,
  ) async {
    try {
      final notificationsResponse = await notificationsRepository
          .getNotifications(params.searchModel);

      // Convert API response to UI model with localization
      final uiData = NotificationListUiData.fromNotificationResponseWithLanguage(
        notificationsResponse,
        params.languageCode,
      );

      return Right(uiData);
    } catch (error) {
      ErrorApiModel errorApiModel = error as ErrorApiModel;
      return Left(errorApiModel);
    }
  }
}
