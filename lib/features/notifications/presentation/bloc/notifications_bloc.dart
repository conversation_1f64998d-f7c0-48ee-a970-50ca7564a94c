import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/features/notifications/data/models/notification_api_model.dart';
import 'package:daleel/features/notifications/domain/entities/notification_ui_model.dart';
import 'package:daleel/features/notifications/domain/usecases/get_notifications_usecase.dart';

part 'notifications_event.dart';
part 'notifications_state.dart';

class NotificationsBloc extends Bloc<NotificationsEvent, NotificationsState> {
  final GetNotificationsUseCase getNotificationsUseCase;

  static const int _pageSize = 20;
  List<NotificationUiModel> _allNotifications = [];
  int _currentPage = 1;
  bool _hasMoreData = true;

  NotificationsBloc({
    required this.getNotificationsUseCase,
  }) : super(const NotificationsInitialState()) {
    on<LoadNotificationsEvent>(_onLoadNotifications);
    on<RefreshNotificationsEvent>(_onRefreshNotifications);
    on<LoadMoreNotificationsEvent>(_onLoadMoreNotifications);
  }

  Future<void> _onLoadNotifications(
    LoadNotificationsEvent event,
    Emitter<NotificationsState> emit,
  ) async {
    emit(const NotificationsLoadingState());
    
    _currentPage = 1;
    _allNotifications.clear();
    _hasMoreData = true;

    await _fetchNotifications(emit);
  }

  Future<void> _onRefreshNotifications(
    RefreshNotificationsEvent event,
    Emitter<NotificationsState> emit,
  ) async {
    _currentPage = 1;
    _allNotifications.clear();
    _hasMoreData = true;

    await _fetchNotifications(emit);
  }

  Future<void> _onLoadMoreNotifications(
    LoadMoreNotificationsEvent event,
    Emitter<NotificationsState> emit,
  ) async {
    if (!_hasMoreData || state is! NotificationsLoadedState) return;

    final currentState = state as NotificationsLoadedState;
    emit(currentState.copyWith(isLoadingMore: true));

    _currentPage++;
    await _fetchNotifications(emit);
  }

  Future<void> _fetchNotifications(Emitter<NotificationsState> emit) async {
    final searchModel = NotificationSearchModel(
      page: _currentPage,
      pageSize: _pageSize,
    );

    final Either<ErrorApiModel, NotificationListUiData> response =
        await getNotificationsUseCase(searchModel);

    response.fold(
      (error) {
        emit(NotificationsErrorState(
          error.message,
          isMessageLocalizationKey: error.isMessageLocalizationKey,
        ));
      },
      (notificationData) {
        if (_currentPage == 1) {
          _allNotifications = notificationData.notifications;
        } else {
          _allNotifications.addAll(notificationData.notifications);
        }

        _hasMoreData = notificationData.notifications.length == _pageSize;

        if (_allNotifications.isEmpty) {
          emit(const NotificationsEmptyState());
        } else {
          emit(NotificationsLoadedState(
            notifications: List.from(_allNotifications),
            hasMoreData: _hasMoreData,
            isLoadingMore: false,
            currentPage: _currentPage,
            totalCount: notificationData.totalAllData,
          ));
        }
      },
    );
  }
}
