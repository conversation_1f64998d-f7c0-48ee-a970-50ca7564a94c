part of 'notifications_bloc.dart';

abstract class NotificationsState extends Equatable {
  const NotificationsState();

  @override
  List<Object?> get props => [];
}

class NotificationsInitialState extends NotificationsState {
  const NotificationsInitialState();
}

class NotificationsLoadingState extends NotificationsState {
  const NotificationsLoadingState();
}

class NotificationsLoadedState extends NotificationsState {
  final List<NotificationUiModel> notifications;
  final bool hasMoreData;
  final bool isLoadingMore;
  final int currentPage;
  final int totalCount;

  const NotificationsLoadedState({
    required this.notifications,
    required this.hasMoreData,
    required this.isLoadingMore,
    required this.currentPage,
    required this.totalCount,
  });

  @override
  List<Object?> get props => [
        notifications,
        hasMoreData,
        isLoadingMore,
        currentPage,
        totalCount,
      ];

  NotificationsLoadedState copyWith({
    List<NotificationUiModel>? notifications,
    bool? hasMoreData,
    bool? isLoadingMore,
    int? currentPage,
    int? totalCount,
  }) {
    return NotificationsLoadedState(
      notifications: notifications ?? this.notifications,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      currentPage: currentPage ?? this.currentPage,
      totalCount: totalCount ?? this.totalCount,
    );
  }
}

class NotificationsErrorState extends NotificationsState {
  final String message;
  final bool isMessageLocalizationKey;

  const NotificationsErrorState(
    this.message, {
    this.isMessageLocalizationKey = false,
  });

  @override
  List<Object?> get props => [message, isMessageLocalizationKey];
}

class NotificationsEmptyState extends NotificationsState {
  const NotificationsEmptyState();
}
