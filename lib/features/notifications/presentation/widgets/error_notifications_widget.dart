import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';

class ErrorNotificationsWidget extends StatelessWidget {
  final String errorMessage;
  final VoidCallback onRetry;

  const ErrorNotificationsWidget({
    super.key,
    required this.errorMessage,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    
    return Container(
      color: AppColors.whiteIcon,
      child: Center(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Error icon with background
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppColors.redButtonColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.error_outline_rounded,
                  size: 60,
                  color: AppColors.redButtonColor.withValues(alpha: 0.7),
                ),
              ),
              
              SizedBox(height: 32),
              
              // Error title
              Text(
                context.translate(LocalizationKeys.somethingWentWrong),
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.headlineLarge,
                ),
                textAlign: TextAlign.center,
                textDirection: !context.isEnglish ? TextDirection.rtl : TextDirection.ltr,
              ),
              
              SizedBox(height: 12.h),
              
              // Error message
              Text(
                errorMessage,
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.subTitleGray,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
                textDirection: !context.isEnglish ? TextDirection.rtl : TextDirection.ltr,
              ),
              
              SizedBox(height: 32.h),
              
              // Retry button
              ElevatedButton.icon(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.colorPrimary,
                  foregroundColor: AppColors.whiteIcon,
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 2,
                ),
                icon: Icon(
                  Icons.refresh,
                  size: 18,
                ),
                label: Text(
                  context.translate(LocalizationKeys.retry),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              SizedBox(height: 16.h),
              
              // Help text
              Text(
                context.translate(LocalizationKeys.somethingWentWrong),
                // !context.isEnglish 
                //     ? 'تأكد من اتصالك بالإنترنت وحاول مرة أخرى'
                //     : 'Check your internet connection and try again',
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.lightSubtitleGray,
                ),
                textAlign: TextAlign.center,
                textDirection: !context.isEnglish ? TextDirection.rtl : TextDirection.ltr,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
