import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';

class LoadingNotificationsWidget extends StatelessWidget {
  const LoadingNotificationsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.whiteIcon,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Loading indicator with custom styling
            Container(
              width: 60,
              height: 60,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.whiteIcon,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.buttonBlackTextColor.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: const CircularProgressIndicator(
                color: AppColors.colorPrimary,
                strokeWidth: 3,
              ),
            ),
            
            SizedBox(height: 24),
            
            // Loading text
            Text(
              context.translate(LocalizationKeys.loadNotification),
              style: TextStyle(
                fontSize: 14,
                color: AppColors.subTitleGray,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class LoadMoreNotificationsWidget extends StatelessWidget {
  const LoadMoreNotificationsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 20),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: const CircularProgressIndicator(
                color: AppColors.colorPrimary,
                strokeWidth: 2,
              ),
            ),
            SizedBox(width: 12.w),
            Text(
              context.translate(LocalizationKeys.loadMore),
              style: TextStyle(
                fontSize: 12,
                color: AppColors.subTitleGray,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
