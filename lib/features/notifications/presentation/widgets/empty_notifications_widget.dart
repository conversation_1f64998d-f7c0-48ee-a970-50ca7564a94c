import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';

class EmptyNotificationsWidget extends StatelessWidget {
  const EmptyNotificationsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    
    return Container(
      color: AppColors.whiteIcon,
      child: Center(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Notification icon with background
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppColors.colorPrimary.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.notifications_none_outlined,
                  size: 60,
                  color: AppColors.colorPrimary.withValues(alpha: 0.6),
                ),
              ),
              
              SizedBox(height: 32),
              
              // Main message
              Text(
                context.translate(LocalizationKeys.noNotifications),
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.headlineLarge,
                ),
                textAlign: TextAlign.center,
                textDirection: !context.isEnglish ? TextDirection.rtl : TextDirection.ltr,
              ),
              
              SizedBox(height: 12.h),
              
              // Subtitle message
              Text(
                context.translate(LocalizationKeys.noNotificationsTitle),
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.subTitleGray,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
                textDirection: !context.isEnglish ? TextDirection.rtl : TextDirection.ltr,
              ),
              
              SizedBox(height: 40),
              
              // Refresh hint
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.colorPrimary.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.colorPrimary.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.refresh,
                      size: 16,
                      color: AppColors.colorPrimary,
                    ),
                    SizedBox(width: 8),
                    Text(
                      context.translate(LocalizationKeys.pullToRefresh),
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.colorPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                      textDirection: !context.isEnglish ? TextDirection.rtl : TextDirection.ltr,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
