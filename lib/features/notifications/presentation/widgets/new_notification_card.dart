import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:daleel/features/notifications/domain/entities/notification_ui_model.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/utils/format/app_date_format.dart';

class NewNotificationCard extends StatelessWidget {
  final NotificationUiModel notification;
  final VoidCallback? onTap;

  const NewNotificationCard({
    super.key,
    required this.notification,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: AppColors.whiteText,
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade200, width: 0.5),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Transform.translate(
                      offset: Offset(0, -24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Stack(
                            children: [
                              _buildAvatar(context),
                              // Online status indicator (green dot)
                              Positioned(
                                bottom: 0,
                                left: !context.isEnglish ? null : 2,
                                right: !context.isEnglish ? 2 : null,
                                child: Container(
                                  width: 16,
                                  height: 16,
                                  decoration: BoxDecoration(
                                    color: AppColors.tagGreenColor,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: AppColors.whiteText,
                                      width: 0.5,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          // Name with badge
                          Row(
                            children: [
                              Text(
                                notification.userName,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.colorPrimary,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textDirection:
                                    !context.isEnglish
                                        ? TextDirection.rtl
                                        : TextDirection.ltr,
                              ),
                              SizedBox(height: 6),
                              // Title
                              Text(
                                notification.title,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.bodyMedium,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textDirection:
                                    !context.isEnglish
                                        ? TextDirection.rtl
                                        : TextDirection.ltr,
                              ),
                            ],
                          ),

                          SizedBox(height: 4),

                          // Description
                          Text(
                            notification.description,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: AppColors.subTitleGray,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textDirection:
                                !context.isEnglish
                                    ? TextDirection.rtl
                                    : TextDirection.ltr,
                          ),

                          SizedBox(height: 8),

                          // Date and time
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 12,
                                color: AppColors.subTitleGray,
                              ),
                              SizedBox(width: 4),
                              Text(
                                AppDateFormat.formatDateWithTime(
                                  notification.createdAt,
                                  context.languageCode,
                                ),
                                style: TextStyle(
                                  fontSize: 11,
                                  color: AppColors.subTitleGray,
                                ),
                                textDirection:
                                    !context.isEnglish
                                        ? TextDirection.rtl
                                        : TextDirection.ltr,
                              ),
                              Spacer(),
                              if (notification.isNew) ...[
                                SizedBox(width: 8),
                                _buildNewBadge(context),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Avatar with online status
                  ],
                ),
              ),

              // Right icon
              // _buildRightIcon(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(shape: BoxShape.circle),
      child: ClipOval(
        child:
            notification.hasUserAvatar
                ? Image.asset(
                  AppAssetPaths.ic_user_icon,
                  fit: BoxFit.cover,
                  width: 48,
                  height: 48,
                )
                : Container(
                  color: AppColors.colorPrimary.withValues(alpha: 0.1),
                  child: Icon(
                    Icons.person,
                    size: 24,
                    color: AppColors.colorPrimary,
                  ),
                ),
      ),
    );
  }

  Widget _buildNewBadge(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.pink.shade100,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        context.translate(LocalizationKeys.newNotification),
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: Colors.pink.shade700,
        ),
      ),
    );
  }

  // Widget _buildRightIcon() {
  //   return Container(
  //     width: 40.w,
  //     height: 40.h,
  //     decoration: BoxDecoration(
  //       color: _getIconBackgroundColor(),
  //       borderRadius: BorderRadius.circular(8.r),
  //     ),
  //     child: Icon(
  //       _getNotificationIcon(),
  //       size: 20,
  //       color: _getIconColor(),
  //     ),
  //   );
  // }

  // Color _getIconBackgroundColor() {
  //   // Use light green for document/file icons as shown in the design
  //   return Colors.green.withValues(alpha: 0.1);
  // }

  // Color _getIconColor() {
  //   // Use green color for icons as shown in the design
  //   return Colors.green.shade600;
  // }

  // IconData _getNotificationIcon() {
  //   // Use document icon as shown in the design
  //   return Icons.description_outlined;
  // }
}
