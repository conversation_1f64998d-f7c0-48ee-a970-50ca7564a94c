import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/core/widgets/base_stateful_screen_widget.dart';
import 'package:daleel/features/notifications/data/datasources/notifications_api_manager.dart';
import 'package:daleel/features/notifications/data/repository/notifications_repository.dart';
import 'package:daleel/features/notifications/domain/usecases/get_notifications_usecase.dart';
import 'package:daleel/features/notifications/presentation/bloc/notifications_bloc.dart';
import 'package:daleel/features/notifications/presentation/widgets/notifications_widgets.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

class NotificationsScreen extends StatelessWidget {
  static const String routeName = '/notifications';
  
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final dioApiManager = GetIt.I<DioApiManager>();
    final notificationsRepository = NotificationsRepository(
      NotificationsApiManager(dioApiManager),
    );
    final getNotificationsUseCase = GetNotificationsUseCase(notificationsRepository);

    return BlocProvider<NotificationsBloc>(
      create: (context) => NotificationsBloc(
        getNotificationsUseCase: getNotificationsUseCase,
      )..add(const LoadNotificationsEvent()),
      child: const NotificationsScreenWithBloc(),
    );
  }
}

class NotificationsScreenWithBloc extends BaseStatefulScreenWidget {
  const NotificationsScreenWithBloc({super.key});

  @override
  BaseScreenState<NotificationsScreenWithBloc> baseScreenCreateState() =>
      _NotificationsScreenWithBlocState();
}

class _NotificationsScreenWithBlocState
    extends BaseScreenState<NotificationsScreenWithBloc> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final state = context.read<NotificationsBloc>().state;
      if (state is NotificationsLoadedState && 
          state.hasMoreData && 
          !state.isLoadingMore) {
        context.read<NotificationsBloc>().add(const LoadMoreNotificationsEvent());
      }
    }
  }

  @override
  Widget baseScreenBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.colorCertificateBackground,
      appBar: _buildAppBar(context),
      body: BlocBuilder<NotificationsBloc, NotificationsState>(
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: () async {
              context.read<NotificationsBloc>().add(const RefreshNotificationsEvent());
              // Wait a bit for the refresh to complete
              await Future.delayed(const Duration(milliseconds: 500));
            },
            color: AppColors.colorPrimary,
            backgroundColor: AppColors.whiteIcon,
            strokeWidth: 2.5,
            child: _buildBody(context, state),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {

    return AppBar(
      title: Text(
        context.translate(LocalizationKeys.notifications),
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.buttonBlackTextColor,
        ),
      ),
      backgroundColor: AppColors.colorCertificateBackground,
      foregroundColor: AppColors.buttonBlackTextColor,
      centerTitle: true,
      elevation: 0.2,
      leading: IconButton(
        color: AppColors.buttonBlackTextColor,
        icon: Icon(
          context.isEnglish ? Icons.arrow_forward_ios : Icons.arrow_back_ios,
          size: 18,
        ),
        onPressed: () => context.pop(),
      ),
      actions: [
        // Add more options if needed
        SizedBox(width: 16),
      ],
    );
  }

  Widget _buildBody(BuildContext context, NotificationsState state) {
    if (state is NotificationsLoadingState) {
      return _buildLoadingState();
    } else if (state is NotificationsErrorState) {
      return _buildErrorState(context, state);
    } else if (state is NotificationsEmptyState) {
      return _buildEmptyState(context);
    } else if (state is NotificationsLoadedState) {
      return _buildLoadedState(context, state);
    }
    
    return _buildLoadingState();
  }

  Widget _buildLoadingState() {
    return const LoadingNotificationsWidget();
  }

  Widget _buildErrorState(BuildContext context, NotificationsErrorState state) {
    return ErrorNotificationsWidget(
      errorMessage: state.message,
      onRetry: () {
        context.read<NotificationsBloc>().add(const LoadNotificationsEvent());
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return const EmptyNotificationsWidget();
  }

  Widget _buildLoadedState(BuildContext context, NotificationsLoadedState state) {
  return Container(
    color: AppColors.colorCertificateBackground,
    child: ListView.separated(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16 , vertical: 40),
      itemCount: state.notifications.length + (state.isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == state.notifications.length) {
          return _buildLoadMoreIndicator();
        }

        final notification = state.notifications[index];
        return NewNotificationCard(
          notification: notification,
          onTap: () {
            _onNotificationTap(context, notification);
          },
        );
      },
      separatorBuilder: (context, index) => const SizedBox(height: 50), // المسافة بين الكروت
    ),
  );
}

  Widget _buildLoadMoreIndicator() {
    return const LoadMoreNotificationsWidget();
  }

  void _onNotificationTap(BuildContext context, notification) {
    // Handle notification tap - could navigate to detail screen or mark as read
    // For now, just show a snackbar
    // ScaffoldMessenger.of(context).showSnackBar(
    //   SnackBar(
    //     content: Text('تم النقر على: ${notification.title}'),
    //     duration: const Duration(seconds: 2),
    //   ),
    // );
  }
}
