class LocalizedText {
  final String en;
  final String ar;

  LocalizedText({
    required this.en,
    required this.ar,
  });

  factory LocalizedText.fromJson(Map<String, dynamic> json) {
    return LocalizedText(
      en: json['en'] ?? '',
      ar: json['ar'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'en': en,
      'ar': ar,
    };
  }

  /// هنا بتحدد أي قيمة ترجّع حسب اللغة الحالية
  String getValue(String languageCode) {
    if (languageCode == 'en') {
      return en.isNotEmpty ? en : (ar.isNotEmpty ? ar : '');
    } else {
      return ar.isNotEmpty ? ar : (en.isNotEmpty ? en : '');
    }
  }
}

class NotificationApiModel {
  final int id;
  final LocalizedText userName;
  final LocalizedText title;
  final LocalizedText description;
  final bool isRead;
  final String createdDate;
  final String pageLink;

  NotificationApiModel({
    required this.id,
    required this.userName,
    required this.title,
    required this.description,
    required this.isRead,
    required this.createdDate,
    required this.pageLink,
  });

  factory NotificationApiModel.fromJson(Map<String, dynamic> json) {
    return NotificationApiModel(
      id: json['id'] ?? 0,
      userName: LocalizedText.fromJson(json['userName'] ?? {}),
      title: LocalizedText.fromJson(json['title'] ?? {}),
      description: LocalizedText.fromJson(json['description'] ?? {}),
      isRead: json['isRead'] ?? false,
      createdDate: json['createdDate'] ?? '',
      pageLink: json['pageLink'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userName': userName.toJson(),
      'title': title.toJson(),
      'description': description.toJson(),
      'isRead': isRead,
      'createdDate': createdDate,
      'pageLink': pageLink,
    };
  }
}

class NotificationResponse {
  final int totalAllData;
  final int total;
  final List<NotificationApiModel> data;

  NotificationResponse({
    required this.totalAllData,
    required this.total,
    required this.data,
  });

  factory NotificationResponse.fromJson(Map<String, dynamic> json) {
    return NotificationResponse(
      totalAllData: json['totalAllData'] ?? 0,
      total: json['total'] ?? 0,
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => NotificationApiModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalAllData': totalAllData,
      'total': total,
      'data': data.map((e) => e.toJson()).toList(),
    };
  }
}

class NotificationSearchModel {
  final int page;
  final int pageSize;

  NotificationSearchModel({
    required this.page,
    required this.pageSize,
  });

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'pageSize': pageSize,
    };
  }
}
