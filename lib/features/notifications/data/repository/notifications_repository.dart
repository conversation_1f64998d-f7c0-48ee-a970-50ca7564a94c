import 'package:daleel/features/notifications/data/datasources/notifications_api_manager.dart';
import 'package:daleel/features/notifications/data/models/notification_api_model.dart';
import 'package:daleel/features/notifications/domain/repositories/base_notifications_repository.dart';

class NotificationsRepository implements BaseNotificationsRepository {
  final NotificationsApiManager notificationsApiManager;

  NotificationsRepository(this.notificationsApiManager);

  @override
  Future<NotificationResponse> getNotifications(NotificationSearchModel searchModel) async {
    late NotificationResponse response;
    await notificationsApiManager.getNotificationsApi(
      searchModel.toJson(),
      (notificationsResponse) {
        response = notificationsResponse;
      },
      (errorApiModel) {
        throw errorApiModel;
      },
    );
    return response;
  }
}
