import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/apis/api_urls.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/features/notifications/data/models/notification_api_model.dart';

class NotificationsApiManager {
  final DioApiManager dioApiManager;

  NotificationsApiManager(this.dioApiManager);

  Future<void> getNotificationsApi(
    Map<String, dynamic> requestBody,
    void Function(NotificationResponse) success,
    void Function(ErrorApiModel) fail,
  ) async {
    await dioApiManager.dio
        .post(ApiUrls.notifications, data: requestBody)
        .then((response) {
          Map<String, dynamic> extractedData =
              response.data as Map<String, dynamic>;
          NotificationResponse notifications = NotificationResponse.fromJson(extractedData);
          success(notifications);
        })
        .catchError((error) {
          fail(ErrorApiModel.identifyError(error: error));
        });
  }
}
