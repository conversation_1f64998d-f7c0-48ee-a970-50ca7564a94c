import 'package:daleel/features/webview/web_views_urls.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import '../../../logic/user_manager.dart';
import '../../webview/dynamic_web_view.dart';

class ChildrenDetailsScreen extends StatefulWidget {
  const ChildrenDetailsScreen({super.key, required this.studentData});

  final Map<String, String> studentData;
  static const routeName = "/children_details";
  static const schoolYearKey = "schoolYearKey";
  static const studentIdKey = "/studentIdKey";

  @override
  State<ChildrenDetailsScreen> createState() => _ChildrenDetailsScreenState();
}
class _ChildrenDetailsScreenState extends State<ChildrenDetailsScreen> {
  final guardianId = GetIt.I<UserMangers>().getGuardianId();

  String childrenDetailsUrl = '';
  static const String redirectUrl =
      'https://daleel-spea-front-qa-cpcscfarfndddcbk.eastus-01.azurewebsites.net/signup-success';

  @override
  void initState() {
    super.initState();
    childrenDetailsUrl = '${WebViewsUrls.baseWebViewUrl}/parent/$guardianId/student/${widget.studentData[ChildrenDetailsScreen.studentIdKey]}?schoolYearId=${widget.studentData[ChildrenDetailsScreen.schoolYearKey]}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: DynamicWebView(
        url: childrenDetailsUrl,
        redirectUrl: redirectUrl,
        onSuccessCallback: () => _onWebViewSuccess(context),
        onFailureCallback: () => _onWebViewFailure(context),
      ),
    );
  }

  /// WebView event handlers to avoid code duplication
  void _onWebViewSuccess(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Success')),
    );
  }

  void _onWebViewFailure(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Failed')),
    );
  }
}
