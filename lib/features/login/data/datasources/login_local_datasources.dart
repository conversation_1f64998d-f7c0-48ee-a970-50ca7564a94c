import 'package:get_it/get_it.dart';
import 'package:daleel/features/login/data/models/login_response_api_model.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/preferences/preferences_manager.dart';

class LoginLocaleManager {
  final PreferencesManager preferencesManager;

  LoginLocaleManager(this.preferencesManager);

  final UserMangers userMangers = GetIt.I<UserMangers>();

  Future<void> setUserLogin() async {
    await preferencesManager.setLoggedIn();
    userMangers.setLoggedInUser();
  }

  Future<void> saveUserInfo(LoginResponseApiModel loginResponseModel) async {
    // Save token
    await preferencesManager.setAccessToken(loginResponseModel.token);

    // Save individual user properties (for backward compatibility)
    await preferencesManager.setUserId(loginResponseModel.user.id.toString());
    await preferencesManager.setUserFullNameEn(
      loginResponseModel.user.fullName,
    );
    await preferencesManager.setUserFullNameAr(
      loginResponseModel.user.arabicFullName,
    );
    await preferencesManager.setEmail(loginResponseModel.user.email);
    await preferencesManager.setPhone(loginResponseModel.user.phoneNumber);

    if (loginResponseModel.user.photoPath != null) {
      await preferencesManager.setProfileImage(
        loginResponseModel.user.photoPath.toString(),
      );
    }

    // Save complete user object as JSON
    await preferencesManager.setUserObject(loginResponseModel.user);

    await userMangers.setUserInfoAndModeFromLocal();
  }

}
