import 'dart:async';

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:daleel/core/failures.dart';
import 'package:daleel/core/usecase.dart';

class LoginValidateUseCase extends BaseUseCase<bool, GlobalKey<FormState>> {
  LoginValidateUseCase();

  @override
  FutureOr<Either<Failure, bool>> call(GlobalKey<FormState> params) async {
    if (params.currentState?.validate() ?? false) {
      params.currentState?.save();
      return const Right(true);
    } else {
      return Left(CacheFailure());
    }
  }
}
