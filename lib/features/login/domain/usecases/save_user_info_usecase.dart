import 'package:dartz/dartz.dart';
import 'package:daleel/core/failures.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/login/data/models/login_response_api_model.dart';
import 'package:daleel/features/login/domain/repositories/base_login_repository.dart';

class SaveUserInfoUseCase extends BaseUseCase<bool, LoginResponseApiModel> {
  BaseLoginRepository loginRepository;

  SaveUserInfoUseCase(this.loginRepository);

  @override
  Future<Either<Failure, bool>> call(LoginResponseApiModel params) async {
    late Either<Failure, bool> state;
    await loginRepository
        .saveUserInfo(params)
        .then((saved) {
          state = const Right(true);
        })
        .catchError((onError) {
          state = Left(CacheFailure());
        });
    return state;
  }
}
