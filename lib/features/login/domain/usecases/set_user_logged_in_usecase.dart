import 'package:dartz/dartz.dart';
import 'package:daleel/core/failures.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/login/domain/repositories/base_login_repository.dart';

class SetUserLoggedInUseCase extends BaseUseCase<bool, NoParams> {
  BaseLoginRepository loginRepository;
  SetUserLoggedInUseCase(this.loginRepository);
  @override
  Future<Either<Failure, bool>> call(NoParams params) async {
    late Either<Failure, bool> state;
    await loginRepository.setLoginUser().then((success) {
      state = const Right(true);
    }).catchError((onError) {
      state = Left(CacheFailure());
    });
    return state;
  }
}
