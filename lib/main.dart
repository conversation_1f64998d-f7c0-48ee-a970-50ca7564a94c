import 'package:daleel/preferences/preferences_manager.dart';
import 'package:daleel/utils/bloc_observer/app_bloc_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'apis/_base/dio_api_manager.dart';
import 'logic/user_manager.dart';
import 'my_app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]).then((_) async {
    /// setup GetIt Instances ...
    GetIt.I.registerLazySingleton<PreferencesManager>(
      () => PreferencesManager(),
    );

    GetIt.I.registerLazySingleton<DioApiManager>(
      () => DioApiManager(
        preferenceManager: GetIt.I<PreferencesManager>(),
        logOutCallBack: () {
          // AppRouter.mainNavigatorKey.currentState
          //     ?.pushNamedAndRemoveUntil(LoginScreen.routeName, (_) => false);
        },
      ),
    );
    GetIt.I.registerLazySingleton<UserMangers>(
      () =>
          UserMangers(GetIt.I<PreferencesManager>())
            ..setUserInfoAndModeFromLocal(),
    );

    Bloc.observer = AppBlocObserver();
    runApp(const MyApp());
  });
}
