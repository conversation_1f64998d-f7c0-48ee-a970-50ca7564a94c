import 'package:daleel/features/children-details/presentation/children_details_screen.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/screen/home_screen.dart';
import 'package:daleel/features/main_taps/screens/main_taps/presentation/screen/main_taps_screen.dart';
import 'package:daleel/features/profile/presentation/screen/profile_screen.dart';
import 'package:daleel/features/signup/sign_up_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import 'features/add_son/presentation/screen/add_son_selection/add_son_selection_screen.dart';
import 'features/add_son/presentation/screen/add_son_with_id/screen/add_son_with_id_screen.dart';
import 'features/add_son/presentation/screen/add_son_without_id/screen/add_son_without_id_screen.dart';
import 'features/children-categorization/presentation/graduated-children/screen/graduated_children_screen.dart';
import 'features/children-categorization/presentation/registered-children/presentation/screen/registered_children_screen.dart';
import 'features/children-categorization/presentation/unregistered-children/screen/submit_registration_request_screen.dart';
import 'features/children-categorization/presentation/unregistered-children/screen/unregistered_children_screen.dart';
import 'features/children-categorization/presentation/withdrawn-children/screen/withdrawn_children_screen.dart';
import 'features/children-categorization/presentation/withdrawn-children/screen/withdrawn_re_registration_screen.dart';
import 'features/login/presentation/screen/login_screen.dart';
import 'features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'features/onboarding/presentation/screens/onboarding_screen.dart';
import 'features/splash/presentation/screen/splash_screen.dart';

class AppRouter {
  /// main Navigation Router
  static final GlobalKey<NavigatorState> mainNavigatorKey =
      GlobalKey<NavigatorState>();

  static GoRouter router = GoRouter(
    debugLogDiagnostics: true,
    navigatorKey: mainNavigatorKey,
    initialLocation: SplashScreen.routeName,
    routes: <RouteBase>[
      GoRoute(path: '/', redirect: (context, state) => SplashScreen.routeName),
      GoRoute(
        path: SplashScreen.routeName,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: LoginScreen.routeName,
        builder: (context, state) => LoginScreen(),
      ),
      GoRoute(
        path: MainTapsScreen.routeName,
        builder: (context, state) => const MainTapsScreen(),
      ),
      GoRoute(
        path: ProfileScreen.routeName,
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: HomeScreen.routeName,
        builder: (context, state) => HomeScreen(),
      ),
      GoRoute(
        path: OnboardingScreen.routeName,
        builder:
            (context, state) => BlocProvider(
              create: (_) => OnboardingBloc(),
              child: const OnboardingScreen(),
            ),
      ),
      GoRoute(
        path: AddSonSelectionScreen.routeName,
        builder: (context, state) => const AddSonSelectionScreen(),
      ),
      GoRoute(
        path: AddSonWithIdScreen.routeName,
        builder: (context, state) => const AddSonWithIdScreen(),
      ),
      GoRoute(
        path: AddSonWithoutIdScreen.routeName,
        builder: (context, state) => const AddSonWithoutIdScreen(),
      ),
      GoRoute(
        path: RegisteredChildrenScreen.routeName,
        builder: (context, state) => const RegisteredChildrenScreen(),
      ),
      GoRoute(
        path: UnRegisteredChildrenScreen.routeName,
        builder: (context, state) => const UnRegisteredChildrenScreen(),
      ),
      GoRoute(
        path: WithdrawnChildrenScreen.routeName,
        builder: (context, state) => const WithdrawnChildrenScreen(),
      ),
      GoRoute(
        path: GraduatedChildrenScreen.routeName,
        builder: (context, state) => GraduatedChildrenScreen(),
      ),
      GoRoute(
        path: SignUpScreen.routeName,
        builder: (context, state) => const SignUpScreen(),
      ),
      GoRoute(
        path: ChildrenDetailsScreen.routeName,
        builder:
            (context, state) => ChildrenDetailsScreen(
              studentData: state.extra as Map<String, String>,
            ),
      ),
      GoRoute(
        path: WithdrawnReRegistrationScreen.routeName,
        builder: (context, state) => const WithdrawnReRegistrationScreen(),
      ),
      GoRoute(
        path: SubmitRegistrationRequestScreen.routeName,
        builder: (context, state) => const SubmitRegistrationRequestScreen(),
      ),
    ],
  );
}
