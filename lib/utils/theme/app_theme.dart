import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:daleel/res/app_colors.dart';

abstract class BaseAppTheme {
  ThemeData get themeDataLight;

  TextTheme get txtThemeLight;

  ThemeData get themeDataDark;

  TextTheme get txtTheme;

  TextStyle get txtTextStyle;

  static TextTheme get defaultTextTheme =>
      const TextTheme().apply(fontFamily: 'DroidKufi');

  static TextStyle get defaultTextStyle =>
      const TextStyle(fontFamily: 'DroidKufi');

  static String get defaultFontFamily => 'DroidKufi';
}

class LightAppTheme implements BaseAppTheme {
  /// The Light Theme
  @override
  ThemeData get themeDataLight {
    return ThemeData(
      platform: TargetPlatform.android,
      brightness: Brightness.light,
      textTheme: txtThemeLight,
      fontFamily: BaseAppTheme.defaultFontFamily,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.colorSchemeSeed,
        primary: AppColors.colorPrimary,
      ),
      primaryColor: AppColors.colorPrimary,
      scaffoldBackgroundColor: AppColors.scaffoldBackground,
      iconTheme: ThemeData.light().iconTheme.copyWith(
        color: AppColors.iconTheme,
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        foregroundColor: AppColors.floatActionBtnForegroundColor,
        backgroundColor: AppColors.floatActionBtnBackgroundColor,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.bottomNavigationBarBackground,
      ),
      cardTheme: ThemeData.light().cardTheme.copyWith(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
      appBarTheme: ThemeData.light().appBarTheme.copyWith(
        shadowColor: Colors.black,
        surfaceTintColor: Colors.white,
        // shape: const RoundedRectangleBorder(
        //   borderRadius: BorderRadius.vertical(bottom: Radius.circular(5)),
        // ),
        iconTheme: ThemeData.light().iconTheme.copyWith(
          color: AppColors.appBarIconColor,
        ),
        titleTextStyle: txtThemeLight.titleMedium?.copyWith(
          fontSize: 20.sp,
          fontWeight: FontWeight.w600,
          color: AppColors.appBarTextColor,
        ),
        elevation: 3,
        centerTitle: false,
        backgroundColor: AppColors.appBarBackground,
        foregroundColor: Colors.transparent,
      ),
      datePickerTheme: const DatePickerThemeData(
        backgroundColor: AppColors.whiteIcon,
      ),
      // timePickerTheme: const TimePickerThemeData(backgroundColor: Colors.white),
      checkboxTheme: const CheckboxThemeData(
        side: BorderSide(
          color: AppColors.checkboxBorderColor,
          width: 1,
        ), // Border color
      ),
    );
  }

  @override
  TextTheme get txtThemeLight => txtTheme.copyWith(
    // Sherpa Blue
    bodyMedium: txtTheme.bodyMedium?.copyWith(color: AppColors.bodyMedium),
    titleMedium: txtTheme.titleMedium?.copyWith(color: AppColors.titleMedium),
    labelMedium: txtTheme.labelMedium?.copyWith(color: AppColors.labelMedium),
    headlineMedium: txtTheme.headlineMedium?.copyWith(
      color: AppColors.headlineMedium,
    ),
    bodyLarge: txtTheme.bodyLarge?.copyWith(color: AppColors.bodyLarge),
    titleSmall: txtTheme.titleSmall?.copyWith(color: AppColors.titleSmall),
    labelSmall: txtTheme.labelSmall?.copyWith(color: AppColors.labelSmall),
  );

  @override
  ThemeData get themeDataDark => themeDataLight;

  @override
  TextTheme get txtTheme => BaseAppTheme.defaultTextTheme;

  @override
  TextStyle get txtTextStyle => BaseAppTheme.defaultTextStyle;
}
