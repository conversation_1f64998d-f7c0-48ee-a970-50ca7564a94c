import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter/material.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/res/app_colors.dart';

class CustomToastMessage {
  static void showToast({
    required BuildContext context,
    required String message,
    Toast? toastLength = Toast.LENGTH_SHORT,
    ToastGravity? gravity = ToastGravity.BOTTOM,
    int? timeInSecForIosWeb = 1,
    Color? backgroundColor = AppColors.colorPrimary,
    Color? textColor = AppColors.saveTxt,
    double? fontSize = 16.0,
  }) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: toastLength ?? Toast.LENGTH_SHORT,
      gravity: gravity ?? ToastGravity.BOTTOM,
      timeInSecForIosWeb: timeInSecForIosWeb ?? 1,
      backgroundColor: backgroundColor ?? AppColors.colorPrimary,
      textColor: textColor ?? AppColors.saveTxt,
      fontSize: fontSize ?? 16.0,
    );
  }

  static void showLocalizedToast({
    required BuildContext context,
    required String localizationKey,
    Toast? toastLength = Toast.LENGTH_SHORT,
    ToastGravity? gravity = ToastGravity.BOTTOM,
    int? timeInSecForIosWeb = 1,
    Color? backgroundColor = AppColors.colorPrimary,
    Color? textColor = AppColors.saveTxt,
    double? fontSize = 16.0,
  }) {
    String translatedMessage = context.translate(localizationKey);
    showToast(
      context: context,
      message: translatedMessage,
      toastLength: toastLength,
      gravity: gravity,
      timeInSecForIosWeb: timeInSecForIosWeb,
      backgroundColor: backgroundColor,
      textColor: textColor,
      fontSize: fontSize,
    );
  }
}
