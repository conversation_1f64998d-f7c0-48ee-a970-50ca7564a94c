import 'package:flutter/material.dart';
import 'package:daleel/utils/extensions/extension_localization.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:daleel/utils/validations/validator.dart';

mixin AppValidate<T extends StatefulWidget> on State<T> {
  String? textValidator(String? value) {
    final ValidationState validationState = Validator.validateText(value);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.required);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.wrongFormat);
      case ValidationState.valid:
        return null;
    }
  }

  String? dateTimeValidator(DateTime? value) {
    final ValidationState validationState = Validator.validateDateTime(value);
    switch (validationState) {
      case ValidationState.empty:
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.required);
      case ValidationState.valid:
        return null;
    }
  }

  String? textValidatorWithLength(String? value) {
    final ValidationState validationState =
        Validator.validateTextWithLength(value);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.required);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.wrongFormat);
      case ValidationState.valid:
        return null;
    }
  }

  String? numberValidator(String? value) {
    final ValidationState validationState = Validator.validateNumber(value);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.required);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.wrongFormat);
      case ValidationState.valid:
        return null;
    }
  }

  String? numberValidatorWithLength(
      BuildContext context, String? value, int length) {
    final ValidationState validationState =
        Validator.validateNumberWithLength(value, length: length);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.required);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.wrongFormat);
      case ValidationState.valid:
        return null;
    }
  }

  String? decimalNumberValidator(String? value) {
    final ValidationState validationState = Validator.validateDecimal(value);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.required);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.wrongFormat);
      case ValidationState.valid:
        return null;
    }
  }

  String? validateNumberRange(String? value, {int min = 1, int max = 100}) {
    final ValidationState validationState =
        Validator.validateNumberRange(value, min: min, max: max);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.required);
      case ValidationState.formatting:
        return "${context.translate(LocalizationKeys.enterNumberlessThan)} $max";
      case ValidationState.valid:
        return null;
    }
  }

  String? emailOrPhoneNumberValidator(String? value) {
    final ValidationState validationState =
        Validator.validateEmailOrPhoneNumber(value);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.required);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.emailInvalid);
      case ValidationState.valid:
        return null;
    }
  }

  String? emailValidator(String? value) {
    final ValidationState validationState = Validator.validateEmail(value);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.emailRequired);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.emailInvalid);
      case ValidationState.valid:
        return null;
    }
  }

  String? phoneNumberValidator(String? value, {String? dialCode}) {
    final ValidationState validationState =
        Validator.validatePhoneNumber(value, dialCode: dialCode);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.phoneNumberRequired);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.phoneNumberInvalid);
      case ValidationState.valid:
        return null;
    }
  }

  String? confirmPasswordValidator(
      BuildContext context, String? value, String password) {
    final ValidationState validationState =
        Validator.validateTextWithText(value, password);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.required);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.passMatch);
      case ValidationState.valid:
        return null;
    }
  }

  String? passwordValidator(String? value) {
    final ValidationState validationState = Validator.validatePassword(value);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.passwordRequired);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.passInvalid);
      case ValidationState.valid:
        return null;
    }
  }

  String? fullNameValidator(String? value) {
    final ValidationState validationState = Validator.validateName(value);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.required);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.nameInvalid);
      case ValidationState.valid:
        return null;
    }
  }

  String? otpValidator(String? value) {
    final ValidationState validationState =
        Validator.validateNumberWithLength(value, length: 6);
    switch (validationState) {
      case ValidationState.empty:
        return context.translate(LocalizationKeys.required);
      case ValidationState.formatting:
        return context.translate(LocalizationKeys.checkOtpCode);
      case ValidationState.valid:
        return null;
    }
  }
}
