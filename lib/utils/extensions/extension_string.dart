extension ConcatenateAsterisk on String {
  String get concatenateAsterisk {
    return "$this *";
  }
}

extension ConcatenateColumn on String {
  String get concatenateColumn {
    return "$this:";
  }
}

extension ConcatenateExclamation on String {
  String get concatenateExclamation {
    return "$this!";
  }
}

extension ConcatenateComma on String {
  String get concatenateComma {
    return "$this,";
  }
}

extension ConcatenateDash on String {
  String get concatenateDash {
    return "$this-";
  }
}

extension ConcatenateSpace on String {
  String get concatenateSpace {
    return "$this ";
  }
}

extension ConcatenateNewLine on String {
  String get concatenateNewline {
    return "$this\n";
  }
}

extension ConcatenateBrackets on String {
  String get concatenateBrackets {
    return "($this)";
  }
}

extension ConcatenateQuestionMarkEnglish on String {
  String get concatenateQuestionMarkEnglish {
    return "$this?";
  }
}

extension ConcatenateDollarSign on String {
  String get concatenateDollarSign {
    return "\$$this";
  }
}

extension ConcatenateQuestionMarkArabic on String {
  String get concatenateQuestionMarkArabic {
    return "$this؟";
  }
}

extension Validation on String? {
  bool get isNullOrEmpty => (this != null && this!.isNotEmpty) ? false : true;

  bool get isNotNullOrNotEmpty =>
      (this != null && this!.isNotEmpty) ? true : false;
}

extension StringExtension on String {
  String get capitalize {
    return "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
  }
}

extension RemoveBracket on String {
  String get removeBracket {
    return replaceAll("[", "").replaceAll("]", "");
  }
}

extension ArabicNumberConverter on String {
  static const Map<String, String> arabicDigits = <String, String>{
    '0': '\u0660',
    '1': '\u0661',
    '2': '\u0662',
    '3': '\u0663',
    '4': '\u0664',
    '5': '\u0665',
    '6': '\u0666',
    '7': '\u0667',
    '8': '\u0668',
    '9': '\u0669',
  };

  String toArabicDigitsConverter() {
    final String number = toString();
    final StringBuffer sb = StringBuffer();
    for (int i = 0; i < number.length; i++) {
      sb.write(arabicDigits[number[i]] ?? number[i]);
    }
    return sb.toString();
  }
}

extension StringValidationExtensions on String {
  /// Returns true if the string is a valid mobile number.
  /// Returns false if the string is an email.
  bool get isMobile {
    // If the string contains '@', it's assumed to be an email.
    if (removeSpaces().contains('@')) return false;

    // Define the mobile phone pattern: starts with '+' and then only digits.
    // Adjust the minimum digits (7 in this example) as needed.
    final RegExp mobilePattern = RegExp(r'^\+\d{7,}$');

    return mobilePattern.hasMatch(removeSpaces().trim());
  }

  bool get isEmail {
    final RegExp emailPattern =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailPattern.hasMatch(removeSpaces().trim());
  }
}

extension StringExtensions on String {
  /// Removes only space characters (' ') from the string.
  String removeSpaces() {
    return replaceAll(' ', '');
  }

  /// Removes all kinds of whitespace (spaces, tabs, newlines) using a regular expression.
  String removeAllWhitespace() {
    return replaceAll(RegExp(r'\s+'), '');
  }
}
