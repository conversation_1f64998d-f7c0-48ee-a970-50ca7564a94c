import 'package:flutter/material.dart';
import 'package:daleel/core/widgets/base_stateful_widget.dart';
import 'package:daleel/core/loading_manager.dart';
import 'package:daleel/res/app_colors.dart';


abstract class BaseStatefulScreenWidget extends BaseStatefulWidget {
  const BaseStatefulScreenWidget({super.key});

  @override
  BaseScreenState baseCreateState() => baseScreenCreateState();

  BaseScreenState baseScreenCreateState();
}

abstract class BaseScreenState<W extends BaseStatefulScreenWidget>
    extends BaseState<W> with LoadingManager {
  @override
  Widget baseBuild(BuildContext context) {
    return Material(
      color: AppColors.scaffoldBackground,
      child: Stack(fit: StackFit.expand, children: [
        baseScreenBuild(context),
        loadingManagerWidget(),
      ]),
    );
  }

  void changeState() {
    setState(() {});
  }

  @override
  void runChangeState() {
    changeState();
  }

  @override
  BuildContext provideContext() {
    return context;
  }

  Widget baseScreenBuild(BuildContext context);
}
