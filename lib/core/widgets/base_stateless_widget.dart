import 'package:flutter/material.dart';
import 'package:daleel/core/platform_manager.dart';
import 'package:daleel/res/app_colors.dart';
export 'package:daleel/core/extensions/extension_localization.dart';
export 'package:daleel/core/extensions/extension_theme.dart';
export 'package:daleel/core/extensions/screen_sizer_extension.dart';

// ignore: must_be_immutable
abstract class BaseStatelessWidget extends StatelessWidget
    with PlatformManager {
  const BaseStatelessWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppColors.scaffoldBackground,
      child: baseBuild(context),
    );
  }

  Widget baseBuild(BuildContext context);
}
