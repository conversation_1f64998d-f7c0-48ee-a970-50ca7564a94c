import 'package:daleel/res/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

/// A dynamic confirmation dialog that can be customized for different use cases
class DynamicConfirmationDialog extends StatelessWidget {
  final String? imagePath;
  final Widget? imageWidget;
  final String title;
  final String? subtitle;
  final String confirmText;
  final String cancelText;
  final VoidCallback onConfirm;
  final VoidCallback? onCancel;
  final Color? confirmButtonColor;
  final Color? cancelButtonColor;

  const DynamicConfirmationDialog({
    super.key,
    this.imagePath,
    this.imageWidget,
    required this.title,
    this.subtitle,
    required this.confirmText,
    required this.cancelText,
    required this.onConfirm,
    this.onCancel,
    this.confirmButtonColor,
    this.cancelButtonColor,
  }) : assert(imagePath != null || imageWidget != null, 'Either imagePath or imageWidget must be provided');

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.85,
            minWidth: 280,
          ),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.whiteIcon,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppColors.bodyMedium.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Dynamic image
              SizedBox(
                width: 100,
                height: 100,
                child: imageWidget ?? Image.asset(imagePath!),
              ),

              SizedBox(height: 24.h),

              // Title text
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.appBarBackground,
                ),
                textAlign: TextAlign.center,
              ),

              if (subtitle != null) ...[
                SizedBox(height: 8.h),
                Text(
                  subtitle!,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: AppColors.bodyMedium,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],

              SizedBox(height: 32.h),

              // Confirm button
              _buildConfirmButton(context),

              SizedBox(height: 12.h),

              // Cancel button
              _buildCancelButton(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: ElevatedButton(
        onPressed: () {
          context.pop();
          onConfirm();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: confirmButtonColor ?? AppColors.colorSecondary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 2,
        ),
        child: Text(
          confirmText,
          style: TextStyle(
            color: AppColors.whiteIcon,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: OutlinedButton(
        onPressed: () {
          context.pop();
          onCancel?.call();
        },
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: cancelButtonColor ?? AppColors.colorSecondary, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        child: Text(
          cancelText,
          style: TextStyle(
            color: cancelButtonColor ?? AppColors.colorSecondary,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  /// Generic method to show any confirmation dialog
  static Future<void> show({
    required BuildContext context,
    String? imagePath,
    Widget? imageWidget,
    required String title,
    String? subtitle,
    required String confirmText,
    required String cancelText,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    Color? confirmButtonColor,
    Color? cancelButtonColor,
    bool barrierDismissible = false,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      builder: (context) => Material(
        type: MaterialType.transparency,
        child: DynamicConfirmationDialog(
          imagePath: imagePath,
          imageWidget: imageWidget,
          title: title,
          subtitle: subtitle,
          confirmText: confirmText,
          cancelText: cancelText,
          onConfirm: onConfirm,
          onCancel: onCancel,
          confirmButtonColor: confirmButtonColor,
          cancelButtonColor: cancelButtonColor,
        ),
      ),
    );
  }


}
