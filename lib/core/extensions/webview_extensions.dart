import '../../features/login/data/models/login_response_api_model.dart';

/// Extension methods for converting objects to WebView-compatible JSON format
extension UserWebViewExtension on User {
  /// Converts User object to JSON format for WebView scripts
  Map<String, dynamic> toWebViewJson() {
    return {
      'id': id,
      'userName': userName,
      'fullName': fullName,
      'arabicFullName': arabicFullName,
      'email': email,
      'phoneNumber': phoneNumber,
      'scope': scope,
      'isActive': isActive,
      'isAdUser': isAdUser,
      'photoPath': photoPath,
      'roles': roles.map((role) => {
        'id': role.id,
        'name': {
          'en': role.name.en,
          'ar': role.name.ar,
        },
        'scope': role.scope,
      }).toList(),
      'claims': claims,
    };
  }
}

/// Extension methods for converting claims list to WebView-compatible JSON format
extension ClaimsWebViewExtension on List<String> {
  /// Converts claims list to JSON format for WebView scripts
  Map<String, dynamic> toWebView<PERSON>son() {
    return {
      'claims': this,
    };
  }
}
